"""
URL configuration for core project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from django.contrib import admin
from django.urls import path
from ninja import NinjaAPI
from accounts.apis import router as accounts_api
from core.utils import HttpException
from merchants.apis import router as merchants_api
from orders.apis import router as orders_api

api = NinjaAPI()

api.add_router("accounts/", accounts_api)
api.add_router("merchants/", merchants_api)
api.add_router("orders/", orders_api)


@api.exception_handler(HttpException)
def service_unavailable(request, exc):
    return api.create_response(
        request,
        {"message": exc.message},
        status=exc.status_code,
    )


urlpatterns = [
    path("admin/", admin.site.urls),
    path("api/", api.urls),
]
