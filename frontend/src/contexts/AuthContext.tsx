import React, { createContext, useContext, useState, useEffect, type ReactNode } from 'react'
import { type UserSchema, type UserOfficeSchema } from '../client/types.gen'
import { client } from '../client/client.gen'
import { accountsApisLogin } from '../client/sdk.gen'

interface AuthContextType {
  user: UserSchema | null
  token: string | null
  selectedOffice: UserOfficeSchema | null
  isAuthenticated: boolean
  isLoading: boolean
  login: (phoneNumber: string, password: string, officeId: string) => Promise<void>
  logout: () => void
  setSelectedOffice: (office: UserOfficeSchema | null) => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

interface AuthProviderProps {
  children: ReactNode
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<UserSchema | null>(null)
  const [token, setToken] = useState<string | null>(null)
  const [selectedOffice, setSelectedOffice] = useState<UserOfficeSchema | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // Check for existing authentication on app start
  useEffect(() => {
    const storedToken = localStorage.getItem('auth_token')
    const storedUser = localStorage.getItem('auth_user')
    const storedOffice = localStorage.getItem('selected_office')

    if (storedToken && storedUser) {
      try {
        const parsedUser = JSON.parse(storedUser)
        const parsedOffice = storedOffice ? JSON.parse(storedOffice) : null

        setToken(storedToken)
        setUser(parsedUser)
        setSelectedOffice(parsedOffice)

        // Set the base URL and auth token in the API client
        client.setConfig({
          baseUrl: 'http://localhost:8000',
          auth: storedToken,
          headers: {
            Authorization: `Bearer ${storedToken}`
          }
        })
      } catch (error) {
        console.error('Error parsing stored auth data:', error)
        // Clear invalid data
        localStorage.removeItem('auth_token')
        localStorage.removeItem('auth_user')
        localStorage.removeItem('selected_office')
      }
    }
    setIsLoading(false)
  }, [])

  const login = async (phoneNumber: string, password: string, officeId: string): Promise<void> => {
    try {
      setIsLoading(true)

      const response = await accountsApisLogin({
        body: {
          phone_number: phoneNumber,
          password: password,
          office_id: officeId,
        }
      })

      if (response.data) {
        const { token: authToken, user: userData } = response.data

        setToken(authToken)
        setUser(userData)
        setSelectedOffice(response.data.office)

        // Store in localStorage
        localStorage.setItem('auth_token', authToken)
        localStorage.setItem('auth_user', JSON.stringify(userData))
        localStorage.setItem('selected_office', JSON.stringify(response.data.office))

        // Set the base URL and auth token in the API client
        client.setConfig({
          baseUrl: 'http://localhost:8000',
          auth: authToken,
          headers: {
            Authorization: `Bearer ${authToken}`
          }
        })
      } else {
        throw new Error('Invalid response from server')
      }
    } catch (error: any) {
      console.error('Login error:', error)

      // Clear any partial state
      setToken(null)
      setUser(null)
      setSelectedOffice(null)
      localStorage.removeItem('auth_token')
      localStorage.removeItem('auth_user')
      localStorage.removeItem('selected_office')

      // Provide user-friendly error messages
      if (error?.response?.status === 401) {
        throw new Error('رقم الهاتف أو كلمة المرور غير صحيحة')
      } else if (error?.response?.status === 403) {
        throw new Error('ليس لديك صلاحية للوصول إلى هذا المكتب')
      } else if (error?.response?.status >= 500) {
        throw new Error('خطأ في الخادم، يرجى المحاولة لاحقاً')
      } else if (!navigator.onLine) {
        throw new Error('لا يوجد اتصال بالإنترنت')
      } else {
        throw new Error('حدث خطأ غير متوقع، يرجى المحاولة مرة أخرى')
      }
    } finally {
      setIsLoading(false)
    }
  }

  const logout = () => {
    setUser(null)
    setToken(null)
    setSelectedOffice(null)

    // Clear localStorage
    localStorage.removeItem('auth_token')
    localStorage.removeItem('auth_user')
    localStorage.removeItem('selected_office')

    // Reset API client config
    client.setConfig({
      baseUrl: 'http://localhost:8000',
      auth: undefined
    })
  }

  const handleSetSelectedOffice = (office: UserOfficeSchema | null) => {
    setSelectedOffice(office)
    if (office) {
      localStorage.setItem('selected_office', JSON.stringify(office))
    } else {
      localStorage.removeItem('selected_office')
    }
  }

  const isAuthenticated = !!user && !!token

  const value: AuthContextType = {
    user,
    token,
    selectedOffice,
    isAuthenticated,
    isLoading,
    login,
    logout,
    setSelectedOffice: handleSetSelectedOffice
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
