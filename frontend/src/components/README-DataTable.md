# Reusable DataTable Component

A flexible, feature-rich data table component built with React, TypeScript, and TanStack Table that can be used across different scenarios in your project.

## Features

- ✅ **Generic TypeScript Support** - Works with any data structure
- ✅ **Drag & Drop Reordering** - Optional row reordering with visual feedback
- ✅ **Row Selection** - Single and multi-row selection with checkboxes
- ✅ **Column Visibility** - Show/hide columns dynamically
- ✅ **Pagination** - Built-in pagination with customizable page sizes
- ✅ **Sorting** - Column sorting (handled by TanStack Table)
- ✅ **Filtering** - Column filtering (handled by TanStack Table)
- ✅ **Custom Actions** - Configurable row actions (Edit, Delete, Copy, etc.)
- ✅ **Add Button** - Optional add new item functionality
- ✅ **Responsive Design** - Works on mobile and desktop
- ✅ **Accessibility** - ARIA labels and keyboard navigation

## Basic Usage

```tsx
import { DataTableComponent } from "@/components/DataTableComponent"
import { type ColumnDef } from "@tanstack/react-table"

// Define your data type
interface User {
  id: string
  name: string
  email: string
  role: string
}

// Define your columns
const columns: ColumnDef<User>[] = [
  {
    accessorKey: "name",
    header: "Name",
  },
  {
    accessorKey: "email",
    header: "Email",
  },
  {
    accessorKey: "role",
    header: "Role",
  },
]

// Use the component
function UserTable() {
  const [users, setUsers] = useState<User[]>([
    { id: "1", name: "John Doe", email: "<EMAIL>", role: "Admin" },
    { id: "2", name: "Jane Smith", email: "<EMAIL>", role: "User" },
  ])

  return (
    <DataTableComponent
      data={users}
      columns={columns}
      idField="id"
    />
  )
}
```

## Advanced Usage

### With Drag & Drop and Custom Actions

```tsx
function AdvancedTable() {
  const [data, setData] = useState<YourDataType[]>([])

  const handleRowDelete = (rowId: string) => {
    setData(prev => prev.filter(item => item.id !== rowId))
  }

  const handleRowReorder = (newData: YourDataType[]) => {
    setData(newData)
  }

  const handleAddClick = () => {
    // Add new item logic
  }

  return (
    <DataTableComponent
      data={data}
      columns={columns}
      enableDragAndDrop={true}
      enableRowSelection={true}
      enableColumnVisibility={true}
      enablePagination={true}
      pageSize={10}
      showAddButton={true}
      addButtonText="Add New Item"
      onAddClick={handleAddClick}
      onRowDelete={handleRowDelete}
      onRowReorder={handleRowReorder}
      idField="id"
      actionsColumn={{
        enableEdit: true,
        enableDelete: true,
        enableCopy: true,
        customActions: [
          {
            label: "Duplicate",
            onClick: (row) => {
              // Custom action logic
            }
          },
          {
            label: "Archive",
            onClick: (row) => {
              // Archive logic
            },
            variant: "destructive"
          }
        ]
      }}
    />
  )
}
```

## API Reference

### DataTableProps<TData>

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `data` | `TData[]` | - | Array of data items to display |
| `columns` | `ColumnDef<TData>[]` | - | Column definitions from TanStack Table |
| `enableDragAndDrop` | `boolean` | `false` | Enable drag & drop row reordering |
| `enableRowSelection` | `boolean` | `true` | Enable row selection with checkboxes |
| `enableColumnVisibility` | `boolean` | `true` | Enable column visibility toggle |
| `enablePagination` | `boolean` | `true` | Enable pagination |
| `pageSize` | `number` | `10` | Number of rows per page |
| `pageSizeOptions` | `number[]` | `[10, 20, 30, 40, 50]` | Available page size options |
| `showAddButton` | `boolean` | `false` | Show add new item button |
| `addButtonText` | `string` | `"Add Item"` | Text for add button |
| `onAddClick` | `() => void` | - | Callback when add button is clicked |
| `onRowUpdate` | `(rowId: string, data: Partial<TData>) => void` | - | Callback when row is updated |
| `onRowDelete` | `(rowId: string) => void` | - | Callback when row is deleted |
| `onRowReorder` | `(newData: TData[]) => void` | - | Callback when rows are reordered |
| `idField` | `keyof TData` | `"id"` | Field to use as unique identifier |
| `className` | `string` | `""` | Additional CSS classes |
| `emptyMessage` | `string` | `"No results."` | Message to show when no data |
| `actionsColumn` | `ActionsColumnConfig` | - | Configuration for actions column |

### ActionsColumnConfig

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `enableEdit` | `boolean` | - | Show edit action |
| `enableCopy` | `boolean` | - | Show copy action |
| `enableFavorite` | `boolean` | - | Show favorite action |
| `enableDelete` | `boolean` | - | Show delete action |
| `customActions` | `CustomAction[]` | - | Array of custom actions |

### CustomAction

| Prop | Type | Description |
|------|------|-------------|
| `label` | `string` | Display text for the action |
| `onClick` | `(row: TData) => void` | Callback when action is clicked |
| `variant` | `"default" \| "destructive"` | Visual variant of the action |

## Examples

### 1. User Management Table

```tsx
function UserManagementTable() {
  const [users, setUsers] = useState<User[]>([])

  const columns: ColumnDef<User>[] = [
    { accessorKey: "name", header: "Name" },
    { accessorKey: "email", header: "Email" },
    {
      accessorKey: "role",
      header: "Role",
      cell: ({ row }) => <Badge variant="outline">{row.original.role}</Badge>
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => (
        <Badge variant={row.original.status === "active" ? "default" : "secondary"}>
          {row.original.status}
        </Badge>
      )
    }
  ]

  return (
    <DataTableComponent
      data={users}
      columns={columns}
      showAddButton={true}
      addButtonText="Add User"
      onAddClick={() => {/* Add user logic */}}
      onRowDelete={(id) => {/* Delete user logic */}}
      actionsColumn={{
        enableEdit: true,
        enableDelete: true,
        customActions: [
          {
            label: "Activate",
            onClick: (user) => {/* Activate user logic */}
          }
        ]
      }}
    />
  )
}
```

### 2. Product Inventory Table

```tsx
function ProductInventoryTable() {
  const [products, setProducts] = useState<Product[]>([])

  const columns: ColumnDef<Product>[] = [
    { accessorKey: "sku", header: "SKU" },
    { accessorKey: "name", header: "Product Name" },
    {
      accessorKey: "price",
      header: "Price",
      cell: ({ row }) => <span>${row.original.price.toFixed(2)}</span>
    },
    {
      accessorKey: "stock",
      header: "Stock",
      cell: ({ row }) => (
        <Badge variant={row.original.stock > 0 ? "default" : "destructive"}>
          {row.original.stock}
        </Badge>
      )
    }
  ]

  return (
    <DataTableComponent
      data={products}
      columns={columns}
      idField="sku"
      showAddButton={true}
      addButtonText="Add Product"
      onAddClick={() => {/* Add product logic */}}
      onRowDelete={(sku) => {/* Delete product logic */}}
      actionsColumn={{
        enableEdit: true,
        enableDelete: true,
        customActions: [
          {
            label: "Restock",
            onClick: (product) => {/* Restock logic */}
          }
        ]
      }}
    />
  )
}
```

### 3. Simple Read-Only Table

```tsx
function SimpleTable() {
  const columns: ColumnDef<SimpleData>[] = [
    { accessorKey: "name", header: "Name" },
    { accessorKey: "value", header: "Value" }
  ]

  return (
    <DataTableComponent
      data={simpleData}
      columns={columns}
      enableRowSelection={false}
      enableColumnVisibility={false}
      enablePagination={false}
      showAddButton={false}
    />
  )
}
```

## Migration from Original DataTable

To migrate from the original `DataTable` component to the new `DataTableComponent`:

1. **Import the new component:**
   ```tsx
   import { DataTableComponent } from "@/components/DataTableComponent"
   ```

2. **Update your column definitions** to remove the drag handle and actions columns (they're now handled automatically):
   ```tsx
   // Before
   const columns = [
     { id: "drag", header: () => null, cell: ({ row }) => <DragHandle /> },
     // ... your columns
     { id: "actions", cell: ({ row }) => <ActionsMenu /> }
   ]

   // After
   const columns = [
     // ... just your data columns
   ]
   ```

3. **Add the appropriate props:**
   ```tsx
   <DataTableComponent
     data={data}
     columns={columns}
     enableDragAndDrop={true} // If you had drag & drop
     actionsColumn={{ enableEdit: true, enableDelete: true }} // If you had actions
     // ... other props as needed
   />
   ```

## Styling

The component uses Tailwind CSS classes and follows the design system. You can customize the appearance by:

1. **Adding custom classes:**
   ```tsx
   <DataTableComponent
     data={data}
     columns={columns}
     className="my-custom-class"
   />
   ```

2. **Customizing cell rendering:**
   ```tsx
   const columns: ColumnDef<YourType>[] = [
     {
       accessorKey: "status",
       header: "Status",
       cell: ({ row }) => (
         <div className="your-custom-styles">
           {row.original.status}
         </div>
       )
     }
   ]
   ```

## Best Practices

1. **Always provide an `idField`** that uniquely identifies each row
2. **Use TypeScript** for better type safety and developer experience
3. **Handle state updates** in your parent component, not inside the table
4. **Use meaningful column headers** and consider accessibility
5. **Implement proper error handling** for async operations
6. **Test with different data sizes** to ensure pagination works correctly

## Dependencies

The component requires these dependencies:
- `@tanstack/react-table`
- `@dnd-kit/core`
- `@dnd-kit/sortable`
- `@dnd-kit/modifiers`
- `@dnd-kit/utilities`
- `@tabler/icons-react`
- `sonner` (for toast notifications)
- Your UI components (Button, Badge, etc.) 