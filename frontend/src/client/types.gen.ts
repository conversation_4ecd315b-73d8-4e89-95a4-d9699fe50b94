// This file is auto-generated by @hey-api/openapi-ts

/**
 * LoginResponseSchema
 */
export type LoginResponseSchema = {
    /**
     * Token
     */
    token: string;
    user: UserSchema;
    office: UserOfficeSchema;
};

/**
 * UserOfficeSchema
 */
export type UserOfficeSchema = {
    /**
     * Id
     */
    id: string;
    /**
     * Merchant Id
     */
    merchant_id: string;
    /**
     * Name
     */
    name: string;
    /**
     * Slug
     */
    slug: string;
    /**
     * Address
     */
    address: string;
};

/**
 * UserSchema
 */
export type UserSchema = {
    /**
     * Id
     */
    id: string;
    /**
     * First Name
     */
    first_name: string;
    /**
     * Last Name
     */
    last_name: string;
    /**
     * Username
     */
    username: string;
    /**
     * Email
     */
    email: string;
    /**
     * Phone Number
     */
    phone_number: string;
    /**
     * Role
     */
    role: string;
    /**
     * Commission Fixed Rate
     */
    commission_fixed_rate: number | null;
    /**
     * Current Location Lat
     */
    current_location_lat: number | null;
    /**
     * Current Location Lng
     */
    current_location_lng: number | null;
};

/**
 * LoginSchema
 */
export type LoginSchema = {
    /**
     * Phone Number
     */
    phone_number: string;
    /**
     * Password
     */
    password: string;
    /**
     * Office Id
     */
    office_id: string;
};

/**
 * UserListResponseSchema
 */
export type UserListResponseSchema = {
    /**
     * Users
     */
    users: Array<UserSchema>;
    /**
     * Total
     */
    total: number;
    /**
     * Page
     */
    page: number;
    /**
     * Page Size
     */
    page_size: number;
};

/**
 * CreateUserSchema
 */
export type CreateUserSchema = {
    /**
     * First Name
     */
    first_name: string;
    /**
     * Last Name
     */
    last_name: string;
    /**
     * Username
     */
    username: string;
    /**
     * Email
     */
    email: string;
    /**
     * Phone Number
     */
    phone_number: string;
    /**
     * Office Id
     */
    office_id: string;
    /**
     * Role
     */
    role: string;
    /**
     * Commission Fixed Rate
     */
    commission_fixed_rate: number;
    /**
     * Password
     */
    password: string;
};

/**
 * UpdateUserSchema
 */
export type UpdateUserSchema = {
    /**
     * First Name
     */
    first_name: string;
    /**
     * Last Name
     */
    last_name: string;
    /**
     * Username
     */
    username: string;
    /**
     * Email
     */
    email: string;
    /**
     * Phone Number
     */
    phone_number: string;
    /**
     * Role
     */
    role: string;
    /**
     * Commission Fixed Rate
     */
    commission_fixed_rate: number;
    /**
     * Current Location Lat
     */
    current_location_lat: number;
    /**
     * Current Location Lng
     */
    current_location_lng: number;
};

/**
 * LocationUpdateSchema
 */
export type LocationUpdateSchema = {
    /**
     * Latitude
     */
    latitude: number;
    /**
     * Longitude
     */
    longitude: number;
};

/**
 * LocationHistoryResponseSchema
 */
export type LocationHistoryResponseSchema = {
    /**
     * Locations
     */
    locations: Array<LocationHistorySchema>;
    /**
     * Total
     */
    total: number;
    /**
     * Page
     */
    page: number;
    /**
     * Page Size
     */
    page_size: number;
};

/**
 * LocationHistorySchema
 */
export type LocationHistorySchema = {
    /**
     * Id
     */
    id: string;
    /**
     * Location Lat
     */
    location_lat: number;
    /**
     * Location Lng
     */
    location_lng: number;
    /**
     * Update Time
     */
    update_time: string;
};

/**
 * UserWalletSchema
 */
export type UserWalletSchema = {
    /**
     * Id
     */
    id: string;
    /**
     * Balance
     */
    balance: number;
    /**
     * Last Update
     */
    last_update: string;
};

/**
 * UserPointsSchema
 */
export type UserPointsSchema = {
    /**
     * Id
     */
    id: string;
    /**
     * Points
     */
    points: number;
    /**
     * Last Update
     */
    last_update: string;
};

/**
 * OfficeListResponseSchema
 */
export type OfficeListResponseSchema = {
    /**
     * Offices
     */
    offices: Array<OfficeSchema>;
    /**
     * Total
     */
    total: number;
    /**
     * Page
     */
    page: number;
    /**
     * Page Size
     */
    page_size: number;
};

/**
 * OfficeSchema
 */
export type OfficeSchema = {
    /**
     * Id
     */
    id: string;
    /**
     * Merchant Id
     */
    merchant_id: string;
    /**
     * Name
     */
    name: string;
    /**
     * Slug
     */
    slug: string;
    /**
     * Address
     */
    address: string;
    /**
     * Phone Number
     */
    phone_number: string;
    /**
     * Email
     */
    email: string;
};

/**
 * CreateOfficeSchema
 */
export type CreateOfficeSchema = {
    /**
     * Merchant Id
     */
    merchant_id: string;
    /**
     * Name
     */
    name: string;
    /**
     * Slug
     */
    slug: string;
    /**
     * Address
     */
    address: string;
    /**
     * Phone Number
     */
    phone_number: string;
    /**
     * Email
     */
    email: string;
};

/**
 * UpdateOfficeSchema
 */
export type UpdateOfficeSchema = {
    /**
     * Name
     */
    name: string;
    /**
     * Slug
     */
    slug: string;
    /**
     * Address
     */
    address: string;
    /**
     * Phone Number
     */
    phone_number: string;
    /**
     * Email
     */
    email: string;
};

/**
 * OfficeEmployeeListResponseSchema
 */
export type OfficeEmployeeListResponseSchema = {
    /**
     * Employees
     */
    employees: Array<OfficeEmployeeSchema>;
    /**
     * Total
     */
    total: number;
    /**
     * Page
     */
    page: number;
    /**
     * Page Size
     */
    page_size: number;
};

/**
 * OfficeEmployeeSchema
 */
export type OfficeEmployeeSchema = {
    /**
     * Id
     */
    id: string;
    /**
     * Office Id
     */
    office_id: string;
    /**
     * User Id
     */
    user_id: string;
    user: UserSchema;
};

/**
 * CreateOfficeEmployeeSchema
 */
export type CreateOfficeEmployeeSchema = {
    /**
     * Office Id
     */
    office_id: string;
    /**
     * User Id
     */
    user_id: string;
};

/**
 * CompanyListResponseSchema
 */
export type CompanyListResponseSchema = {
    /**
     * Companies
     */
    companies: Array<CompanySchema>;
    /**
     * Total
     */
    total: number;
    /**
     * Page
     */
    page: number;
    /**
     * Page Size
     */
    page_size: number;
};

/**
 * CompanySchema
 */
export type CompanySchema = {
    /**
     * Id
     */
    id: string;
    /**
     * Office Id
     */
    office_id: string;
    /**
     * Code
     */
    code: string | null;
    /**
     * Name
     */
    name: string;
    /**
     * Address
     */
    address: string;
    /**
     * Phone
     */
    phone: string;
    /**
     * Color Code
     */
    color_code: string;
    /**
     * Created At
     */
    created_at: string;
    /**
     * Updated At
     */
    updated_at: string;
};

/**
 * CreateCompanySchema
 */
export type CreateCompanySchema = {
    /**
     * Office Id
     */
    office_id: string;
    /**
     * Code
     */
    code: string | null;
    /**
     * Name
     */
    name: string;
    /**
     * Address
     */
    address: string;
    /**
     * Phone
     */
    phone: string;
    /**
     * Color Code
     */
    color_code: string;
};

/**
 * UpdateCompanySchema
 */
export type UpdateCompanySchema = {
    /**
     * Code
     */
    code: string | null;
    /**
     * Name
     */
    name: string;
    /**
     * Address
     */
    address: string;
    /**
     * Phone
     */
    phone: string;
    /**
     * Color Code
     */
    color_code: string;
};

/**
 * CompanyChannelListResponseSchema
 */
export type CompanyChannelListResponseSchema = {
    /**
     * Channels
     */
    channels: Array<CompanyChannelSchema>;
    /**
     * Total
     */
    total: number;
    /**
     * Page
     */
    page: number;
    /**
     * Page Size
     */
    page_size: number;
};

/**
 * CompanyChannelSchema
 */
export type CompanyChannelSchema = {
    /**
     * Id
     */
    id: string;
    /**
     * Merchant Id
     */
    merchant_id: string;
    /**
     * Office Id
     */
    office_id: string;
    /**
     * Company Id
     */
    company_id: string | null;
    /**
     * Name
     */
    name: string;
    /**
     * Notes
     */
    notes: string;
    /**
     * Channel Whatsapp Number
     */
    channel_whatsapp_number: string;
    /**
     * Created At
     */
    created_at: string;
    /**
     * Updated At
     */
    updated_at: string;
};

/**
 * CreateCompanyChannelSchema
 */
export type CreateCompanyChannelSchema = {
    /**
     * Merchant Id
     */
    merchant_id: string;
    /**
     * Office Id
     */
    office_id: string;
    /**
     * Company Id
     */
    company_id: string | null;
    /**
     * Name
     */
    name: string;
    /**
     * Notes
     */
    notes: string;
    /**
     * Channel Whatsapp Number
     */
    channel_whatsapp_number: string;
};

/**
 * UpdateCompanyChannelSchema
 */
export type UpdateCompanyChannelSchema = {
    /**
     * Company Id
     */
    company_id: string | null;
    /**
     * Name
     */
    name: string;
    /**
     * Notes
     */
    notes: string;
    /**
     * Channel Whatsapp Number
     */
    channel_whatsapp_number: string;
};

/**
 * OrderCancellationReasonTemplateListResponseSchema
 */
export type OrderCancellationReasonTemplateListResponseSchema = {
    /**
     * Templates
     */
    templates: Array<OrderCancellationReasonTemplateSchema>;
    /**
     * Total
     */
    total: number;
    /**
     * Page
     */
    page: number;
    /**
     * Page Size
     */
    page_size: number;
};

/**
 * OrderCancellationReasonTemplateSchema
 */
export type OrderCancellationReasonTemplateSchema = {
    /**
     * Id
     */
    id: string;
    /**
     * Office Id
     */
    office_id: string | null;
    /**
     * Name
     */
    name: string;
    /**
     * Description
     */
    description: string;
    order_default_handling_status: OrderHandlingStatusSchema | null;
    /**
     * Just Delivery Commission Rate
     */
    just_delivery_commission_rate: boolean;
    /**
     * Commission Fixed Rate
     */
    commission_fixed_rate: number | null;
    /**
     * Percentage Of Order Total Price
     */
    percentage_of_order_total_price: number | null;
    /**
     * Created At
     */
    created_at: string;
    /**
     * Updated At
     */
    updated_at: string;
};

/**
 * OrderHandlingStatusSchema
 */
export type OrderHandlingStatusSchema = 'PENDING' | 'ASSIGNED' | 'PROCESSING' | 'CANCELLED' | 'DELIVERED';

/**
 * CreateOrderCancellationReasonTemplateSchema
 */
export type CreateOrderCancellationReasonTemplateSchema = {
    /**
     * Office Id
     */
    office_id: string | null;
    /**
     * Name
     */
    name: string;
    /**
     * Description
     */
    description: string;
    order_default_handling_status: OrderHandlingStatusSchema | null;
    /**
     * Just Delivery Commission Rate
     */
    just_delivery_commission_rate: boolean;
    /**
     * Commission Fixed Rate
     */
    commission_fixed_rate: number | null;
    /**
     * Percentage Of Order Total Price
     */
    percentage_of_order_total_price: number | null;
};

/**
 * UpdateOrderCancellationReasonTemplateSchema
 */
export type UpdateOrderCancellationReasonTemplateSchema = {
    /**
     * Name
     */
    name: string;
    /**
     * Description
     */
    description: string;
    order_default_handling_status: OrderHandlingStatusSchema | null;
    /**
     * Just Delivery Commission Rate
     */
    just_delivery_commission_rate: boolean;
    /**
     * Commission Fixed Rate
     */
    commission_fixed_rate: number | null;
    /**
     * Percentage Of Order Total Price
     */
    percentage_of_order_total_price: number | null;
};

/**
 * OrderListResponseSchema
 */
export type OrderListResponseSchema = {
    /**
     * Orders
     */
    orders: Array<OrderSchema>;
    /**
     * Total
     */
    total: number;
    /**
     * Page
     */
    page: number;
    /**
     * Page Size
     */
    page_size: number;
};

/**
 * OrderSchema
 */
export type OrderSchema = {
    /**
     * Id
     */
    id: string;
    /**
     * Office Id
     */
    office_id: string;
    /**
     * Code
     */
    code: string;
    /**
     * Notes
     */
    notes: string;
    /**
     * Total Price
     */
    total_price: number | null;
    /**
     * Customer Name
     */
    customer_name: string;
    /**
     * Customer Phone
     */
    customer_phone: string;
    /**
     * Customer Address
     */
    customer_address: string;
    /**
     * Customer Company Id
     */
    customer_company_id: string | null;
    /**
     * Breakable
     */
    breakable: boolean;
    /**
     * Deadline Date
     */
    deadline_date: string | null;
    /**
     * Commission Fixed Rate
     */
    commission_fixed_rate: number | null;
    /**
     * Assigned To Id
     */
    assigned_to_id: string | null;
    /**
     * Assigned At
     */
    assigned_at: string | null;
    /**
     * Final Customer Payment
     */
    final_customer_payment: number | null;
    handling_status: OrderHandlingStatusSchema;
    /**
     * Cancellation Reason Template Id
     */
    cancellation_reason_template_id: string | null;
    /**
     * Cancellation Reason
     */
    cancellation_reason: string | null;
    /**
     * Created At
     */
    created_at: string;
    /**
     * Updated At
     */
    updated_at: string;
};

/**
 * CreateOrderSchema
 */
export type CreateOrderSchema = {
    /**
     * Office Id
     */
    office_id: string;
    /**
     * Code
     */
    code?: string | null;
    /**
     * Notes
     */
    notes?: string | null;
    /**
     * Total Price
     */
    total_price?: number | null;
    /**
     * Customer Name
     */
    customer_name: string;
    /**
     * Customer Phone
     */
    customer_phone: string;
    /**
     * Customer Address
     */
    customer_address?: string | null;
    /**
     * Customer Company Id
     */
    customer_company_id: string | null;
    /**
     * Breakable
     */
    breakable?: boolean | null;
    /**
     * Deadline Date
     */
    deadline_date?: string | null;
    /**
     * Commission Fixed Rate
     */
    commission_fixed_rate?: number | null;
    /**
     * Assigned To Id
     */
    assigned_to_id?: string | null;
    /**
     * Final Customer Payment
     */
    final_customer_payment?: number | null;
    handling_status?: OrderHandlingStatusSchema;
    /**
     * Cancellation Reason Template Id
     */
    cancellation_reason_template_id: string | null;
    /**
     * Cancellation Reason
     */
    cancellation_reason: string | null;
};

/**
 * BulkCreateOrderResponseSchema
 */
export type BulkCreateOrderResponseSchema = {
    /**
     * Success
     */
    success: boolean;
    /**
     * Created Orders
     */
    created_orders: Array<OrderSchema>;
    /**
     * Failed Orders
     */
    failed_orders: Array<{
        [key: string]: unknown;
    }>;
    /**
     * Total Processed
     */
    total_processed: number;
    /**
     * Successful Count
     */
    successful_count: number;
    /**
     * Failed Count
     */
    failed_count: number;
    /**
     * Errors
     */
    errors: Array<string>;
};

/**
 * UpdateOrderSchema
 */
export type UpdateOrderSchema = {
    /**
     * Code
     */
    code: string;
    /**
     * Notes
     */
    notes: string;
    /**
     * Total Price
     */
    total_price: number | null;
    /**
     * Customer Name
     */
    customer_name: string;
    /**
     * Customer Phone
     */
    customer_phone: string;
    /**
     * Customer Address
     */
    customer_address: string;
    /**
     * Customer Company Id
     */
    customer_company_id: string | null;
    /**
     * Breakable
     */
    breakable: boolean;
    /**
     * Deadline Date
     */
    deadline_date: string | null;
    /**
     * Commission Fixed Rate
     */
    commission_fixed_rate: number | null;
    /**
     * Assigned To Id
     */
    assigned_to_id: string | null;
    /**
     * Final Customer Payment
     */
    final_customer_payment: number | null;
    handling_status: OrderHandlingStatusSchema;
    /**
     * Cancellation Reason Template Id
     */
    cancellation_reason_template_id: string | null;
    /**
     * Cancellation Reason
     */
    cancellation_reason: string | null;
};

/**
 * OrderStatusHistoryListResponseSchema
 */
export type OrderStatusHistoryListResponseSchema = {
    /**
     * History
     */
    history: Array<OrderStatusHistorySchema>;
    /**
     * Total
     */
    total: number;
    /**
     * Page
     */
    page: number;
    /**
     * Page Size
     */
    page_size: number;
};

/**
 * OrderStatusHistorySchema
 */
export type OrderStatusHistorySchema = {
    /**
     * Id
     */
    id: string;
    /**
     * Order Id
     */
    order_id: string;
    status: OrderHandlingStatusSchema;
    /**
     * Notes
     */
    notes: string;
    /**
     * Created By Id
     */
    created_by_id: string;
    /**
     * Created At
     */
    created_at: string;
    /**
     * Updated At
     */
    updated_at: string;
};

/**
 * OrderAssigneeHistoryListResponseSchema
 */
export type OrderAssigneeHistoryListResponseSchema = {
    /**
     * History
     */
    history: Array<OrderAssigneeHistorySchema>;
    /**
     * Total
     */
    total: number;
    /**
     * Page
     */
    page: number;
    /**
     * Page Size
     */
    page_size: number;
};

/**
 * OrderAssigneeHistorySchema
 */
export type OrderAssigneeHistorySchema = {
    /**
     * Id
     */
    id: string;
    /**
     * Order Id
     */
    order_id: string;
    /**
     * Assignee Id
     */
    assignee_id: string;
    /**
     * Assigned By Id
     */
    assigned_by_id: string;
    /**
     * Created At
     */
    created_at: string;
    /**
     * Updated At
     */
    updated_at: string;
};

export type AccountsApisLoginData = {
    body: LoginSchema;
    path?: never;
    query?: never;
    url: '/api/accounts/login';
};

export type AccountsApisLoginResponses = {
    /**
     * OK
     */
    200: LoginResponseSchema;
};

export type AccountsApisLoginResponse = AccountsApisLoginResponses[keyof AccountsApisLoginResponses];

export type AccountsApisLogoutData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/accounts/logout';
};

export type AccountsApisLogoutResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type AccountsApisGetMeData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/accounts/me';
};

export type AccountsApisGetMeResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type AccountsApisListUsersData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Page
         */
        page?: number;
        /**
         * Page Size
         */
        page_size?: number;
        /**
         * Role
         */
        role?: string;
        /**
         * Office Id
         */
        office_id?: string;
    };
    url: '/api/accounts/';
};

export type AccountsApisListUsersResponses = {
    /**
     * OK
     */
    200: UserListResponseSchema;
};

export type AccountsApisListUsersResponse = AccountsApisListUsersResponses[keyof AccountsApisListUsersResponses];

export type AccountsApisCreateUserData = {
    body: CreateUserSchema;
    path?: never;
    query?: never;
    url: '/api/accounts/';
};

export type AccountsApisCreateUserResponses = {
    /**
     * OK
     */
    200: UserSchema;
};

export type AccountsApisCreateUserResponse = AccountsApisCreateUserResponses[keyof AccountsApisCreateUserResponses];

export type AccountsApisDeleteUserData = {
    body?: never;
    path: {
        /**
         * User Id
         */
        user_id: string;
    };
    query?: never;
    url: '/api/accounts/{user_id}';
};

export type AccountsApisDeleteUserResponses = {
    /**
     * Response
     * OK
     */
    200: string;
};

export type AccountsApisDeleteUserResponse = AccountsApisDeleteUserResponses[keyof AccountsApisDeleteUserResponses];

export type AccountsApisGetUserData = {
    body?: never;
    path: {
        /**
         * User Id
         */
        user_id: string;
    };
    query?: never;
    url: '/api/accounts/{user_id}';
};

export type AccountsApisGetUserResponses = {
    /**
     * OK
     */
    200: UserSchema;
};

export type AccountsApisGetUserResponse = AccountsApisGetUserResponses[keyof AccountsApisGetUserResponses];

export type AccountsApisUpdateUserData = {
    body: UpdateUserSchema;
    path: {
        /**
         * User Id
         */
        user_id: string;
    };
    query?: never;
    url: '/api/accounts/{user_id}';
};

export type AccountsApisUpdateUserResponses = {
    /**
     * OK
     */
    200: UserSchema;
};

export type AccountsApisUpdateUserResponse = AccountsApisUpdateUserResponses[keyof AccountsApisUpdateUserResponses];

export type AccountsApisUpdateLocationData = {
    body: LocationUpdateSchema;
    path?: never;
    query?: never;
    url: '/api/accounts/me/location';
};

export type AccountsApisUpdateLocationResponses = {
    /**
     * Response
     * OK
     */
    200: string;
};

export type AccountsApisUpdateLocationResponse = AccountsApisUpdateLocationResponses[keyof AccountsApisUpdateLocationResponses];

export type AccountsApisGetLocationHistoryData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Page
         */
        page?: number;
        /**
         * Page Size
         */
        page_size?: number;
    };
    url: '/api/accounts/me/location/history';
};

export type AccountsApisGetLocationHistoryResponses = {
    /**
     * OK
     */
    200: LocationHistoryResponseSchema;
};

export type AccountsApisGetLocationHistoryResponse = AccountsApisGetLocationHistoryResponses[keyof AccountsApisGetLocationHistoryResponses];

export type AccountsApisGetMyOfficesData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/accounts/me/offices';
};

export type AccountsApisGetMyOfficesResponses = {
    /**
     * OK
     */
    200: UserOfficeSchema;
};

export type AccountsApisGetMyOfficesResponse = AccountsApisGetMyOfficesResponses[keyof AccountsApisGetMyOfficesResponses];

export type AccountsApisGetMeWalletData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/accounts/me/wallet';
};

export type AccountsApisGetMeWalletResponses = {
    /**
     * OK
     */
    200: UserWalletSchema;
};

export type AccountsApisGetMeWalletResponse = AccountsApisGetMeWalletResponses[keyof AccountsApisGetMeWalletResponses];

export type AccountsApisUpdateMeWalletData = {
    body?: never;
    path?: never;
    query: {
        /**
         * Amount
         */
        amount: number;
    };
    url: '/api/accounts/me/wallet';
};

export type AccountsApisUpdateMeWalletResponses = {
    /**
     * OK
     */
    200: UserWalletSchema;
};

export type AccountsApisUpdateMeWalletResponse = AccountsApisUpdateMeWalletResponses[keyof AccountsApisUpdateMeWalletResponses];

export type AccountsApisGetMePointsData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/accounts/me/points';
};

export type AccountsApisGetMePointsResponses = {
    /**
     * OK
     */
    200: UserPointsSchema;
};

export type AccountsApisGetMePointsResponse = AccountsApisGetMePointsResponses[keyof AccountsApisGetMePointsResponses];

export type AccountsApisAddPointsData = {
    body?: never;
    path?: never;
    query: {
        /**
         * Amount
         */
        amount: number;
    };
    url: '/api/accounts/me/points';
};

export type AccountsApisAddPointsResponses = {
    /**
     * OK
     */
    200: UserPointsSchema;
};

export type AccountsApisAddPointsResponse = AccountsApisAddPointsResponses[keyof AccountsApisAddPointsResponses];

export type MerchantsApisListOfficesData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Page
         */
        page?: number;
        /**
         * Page Size
         */
        page_size?: number;
        /**
         * Merchant Id
         */
        merchant_id?: string;
    };
    url: '/api/merchants/offices/';
};

export type MerchantsApisListOfficesResponses = {
    /**
     * OK
     */
    200: OfficeListResponseSchema;
};

export type MerchantsApisListOfficesResponse = MerchantsApisListOfficesResponses[keyof MerchantsApisListOfficesResponses];

export type MerchantsApisCreateOfficeData = {
    body: CreateOfficeSchema;
    path?: never;
    query?: never;
    url: '/api/merchants/offices/';
};

export type MerchantsApisCreateOfficeResponses = {
    /**
     * OK
     */
    200: OfficeSchema;
};

export type MerchantsApisCreateOfficeResponse = MerchantsApisCreateOfficeResponses[keyof MerchantsApisCreateOfficeResponses];

export type MerchantsApisDeleteOfficeData = {
    body?: never;
    path: {
        /**
         * Office Id
         */
        office_id: string;
    };
    query?: never;
    url: '/api/merchants/offices/{office_id}';
};

export type MerchantsApisDeleteOfficeResponses = {
    /**
     * Response
     * OK
     */
    200: string;
};

export type MerchantsApisDeleteOfficeResponse = MerchantsApisDeleteOfficeResponses[keyof MerchantsApisDeleteOfficeResponses];

export type MerchantsApisGetOfficeData = {
    body?: never;
    path: {
        /**
         * Office Id
         */
        office_id: string;
    };
    query?: never;
    url: '/api/merchants/offices/{office_id}';
};

export type MerchantsApisGetOfficeResponses = {
    /**
     * OK
     */
    200: OfficeSchema;
};

export type MerchantsApisGetOfficeResponse = MerchantsApisGetOfficeResponses[keyof MerchantsApisGetOfficeResponses];

export type MerchantsApisUpdateOfficeData = {
    body: UpdateOfficeSchema;
    path: {
        /**
         * Office Id
         */
        office_id: string;
    };
    query?: never;
    url: '/api/merchants/offices/{office_id}';
};

export type MerchantsApisUpdateOfficeResponses = {
    /**
     * OK
     */
    200: OfficeSchema;
};

export type MerchantsApisUpdateOfficeResponse = MerchantsApisUpdateOfficeResponses[keyof MerchantsApisUpdateOfficeResponses];

export type MerchantsApisListEmployeesOfficesByPhoneNumberData = {
    body?: never;
    path?: never;
    query: {
        /**
         * Employee Phone Number
         */
        employee_phone_number: string;
    };
    url: '/api/merchants/offices/get-by-phone-number/';
};

export type MerchantsApisListEmployeesOfficesByPhoneNumberResponses = {
    /**
     * OK
     */
    200: OfficeListResponseSchema;
};

export type MerchantsApisListEmployeesOfficesByPhoneNumberResponse = MerchantsApisListEmployeesOfficesByPhoneNumberResponses[keyof MerchantsApisListEmployeesOfficesByPhoneNumberResponses];

export type MerchantsApisListOfficeEmployeesData = {
    body?: never;
    path: {
        /**
         * Office Id
         */
        office_id: string;
    };
    query?: {
        /**
         * Page
         */
        page?: number;
        /**
         * Page Size
         */
        page_size?: number;
    };
    url: '/api/merchants/offices/{office_id}/employees/';
};

export type MerchantsApisListOfficeEmployeesResponses = {
    /**
     * OK
     */
    200: OfficeEmployeeListResponseSchema;
};

export type MerchantsApisListOfficeEmployeesResponse = MerchantsApisListOfficeEmployeesResponses[keyof MerchantsApisListOfficeEmployeesResponses];

export type MerchantsApisAddOfficeEmployeeData = {
    body: CreateOfficeEmployeeSchema;
    path: {
        /**
         * Office Id
         */
        office_id: string;
    };
    query?: never;
    url: '/api/merchants/offices/{office_id}/employees/';
};

export type MerchantsApisAddOfficeEmployeeResponses = {
    /**
     * OK
     */
    200: OfficeEmployeeSchema;
};

export type MerchantsApisAddOfficeEmployeeResponse = MerchantsApisAddOfficeEmployeeResponses[keyof MerchantsApisAddOfficeEmployeeResponses];

export type MerchantsApisRemoveOfficeEmployeeData = {
    body?: never;
    path: {
        /**
         * Office Id
         */
        office_id: string;
        /**
         * Employee Id
         */
        employee_id: string;
    };
    query?: never;
    url: '/api/merchants/offices/{office_id}/employees/{employee_id}';
};

export type MerchantsApisRemoveOfficeEmployeeResponses = {
    /**
     * Response
     * OK
     */
    200: string;
};

export type MerchantsApisRemoveOfficeEmployeeResponse = MerchantsApisRemoveOfficeEmployeeResponses[keyof MerchantsApisRemoveOfficeEmployeeResponses];

export type OrdersApisListCompaniesData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Page
         */
        page?: number;
        /**
         * Page Size
         */
        page_size?: number;
        /**
         * Office Id
         */
        office_id?: string;
    };
    url: '/api/orders/companies/';
};

export type OrdersApisListCompaniesResponses = {
    /**
     * OK
     */
    200: CompanyListResponseSchema;
};

export type OrdersApisListCompaniesResponse = OrdersApisListCompaniesResponses[keyof OrdersApisListCompaniesResponses];

export type OrdersApisCreateCompanyData = {
    body: CreateCompanySchema;
    path?: never;
    query?: never;
    url: '/api/orders/companies/';
};

export type OrdersApisCreateCompanyResponses = {
    /**
     * OK
     */
    200: CompanySchema;
};

export type OrdersApisCreateCompanyResponse = OrdersApisCreateCompanyResponses[keyof OrdersApisCreateCompanyResponses];

export type OrdersApisDeleteCompanyData = {
    body?: never;
    path: {
        /**
         * Company Id
         */
        company_id: string;
    };
    query?: never;
    url: '/api/orders/companies/{company_id}';
};

export type OrdersApisDeleteCompanyResponses = {
    /**
     * Response
     * OK
     */
    200: string;
};

export type OrdersApisDeleteCompanyResponse = OrdersApisDeleteCompanyResponses[keyof OrdersApisDeleteCompanyResponses];

export type OrdersApisGetCompanyData = {
    body?: never;
    path: {
        /**
         * Company Id
         */
        company_id: string;
    };
    query?: never;
    url: '/api/orders/companies/{company_id}';
};

export type OrdersApisGetCompanyResponses = {
    /**
     * OK
     */
    200: CompanySchema;
};

export type OrdersApisGetCompanyResponse = OrdersApisGetCompanyResponses[keyof OrdersApisGetCompanyResponses];

export type OrdersApisUpdateCompanyData = {
    body: UpdateCompanySchema;
    path: {
        /**
         * Company Id
         */
        company_id: string;
    };
    query?: never;
    url: '/api/orders/companies/{company_id}';
};

export type OrdersApisUpdateCompanyResponses = {
    /**
     * OK
     */
    200: CompanySchema;
};

export type OrdersApisUpdateCompanyResponse = OrdersApisUpdateCompanyResponses[keyof OrdersApisUpdateCompanyResponses];

export type OrdersApisListCompanyChannelsData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Page
         */
        page?: number;
        /**
         * Page Size
         */
        page_size?: number;
        /**
         * Merchant Id
         */
        merchant_id?: string;
        /**
         * Office Id
         */
        office_id?: string;
        /**
         * Company Id
         */
        company_id?: string;
    };
    url: '/api/orders/company-channels/';
};

export type OrdersApisListCompanyChannelsResponses = {
    /**
     * OK
     */
    200: CompanyChannelListResponseSchema;
};

export type OrdersApisListCompanyChannelsResponse = OrdersApisListCompanyChannelsResponses[keyof OrdersApisListCompanyChannelsResponses];

export type OrdersApisCreateCompanyChannelData = {
    body: CreateCompanyChannelSchema;
    path?: never;
    query?: never;
    url: '/api/orders/company-channels/';
};

export type OrdersApisCreateCompanyChannelResponses = {
    /**
     * OK
     */
    200: CompanyChannelSchema;
};

export type OrdersApisCreateCompanyChannelResponse = OrdersApisCreateCompanyChannelResponses[keyof OrdersApisCreateCompanyChannelResponses];

export type OrdersApisDeleteCompanyChannelData = {
    body?: never;
    path: {
        /**
         * Channel Id
         */
        channel_id: string;
    };
    query?: never;
    url: '/api/orders/company-channels/{channel_id}';
};

export type OrdersApisDeleteCompanyChannelResponses = {
    /**
     * Response
     * OK
     */
    200: string;
};

export type OrdersApisDeleteCompanyChannelResponse = OrdersApisDeleteCompanyChannelResponses[keyof OrdersApisDeleteCompanyChannelResponses];

export type OrdersApisGetCompanyChannelData = {
    body?: never;
    path: {
        /**
         * Channel Id
         */
        channel_id: string;
    };
    query?: never;
    url: '/api/orders/company-channels/{channel_id}';
};

export type OrdersApisGetCompanyChannelResponses = {
    /**
     * OK
     */
    200: CompanyChannelSchema;
};

export type OrdersApisGetCompanyChannelResponse = OrdersApisGetCompanyChannelResponses[keyof OrdersApisGetCompanyChannelResponses];

export type OrdersApisUpdateCompanyChannelData = {
    body: UpdateCompanyChannelSchema;
    path: {
        /**
         * Channel Id
         */
        channel_id: string;
    };
    query?: never;
    url: '/api/orders/company-channels/{channel_id}';
};

export type OrdersApisUpdateCompanyChannelResponses = {
    /**
     * OK
     */
    200: CompanyChannelSchema;
};

export type OrdersApisUpdateCompanyChannelResponse = OrdersApisUpdateCompanyChannelResponses[keyof OrdersApisUpdateCompanyChannelResponses];

export type OrdersApisListCancellationTemplatesData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Page
         */
        page?: number;
        /**
         * Page Size
         */
        page_size?: number;
        /**
         * Office Id
         */
        office_id?: string;
    };
    url: '/api/orders/cancellation-templates/';
};

export type OrdersApisListCancellationTemplatesResponses = {
    /**
     * OK
     */
    200: OrderCancellationReasonTemplateListResponseSchema;
};

export type OrdersApisListCancellationTemplatesResponse = OrdersApisListCancellationTemplatesResponses[keyof OrdersApisListCancellationTemplatesResponses];

export type OrdersApisCreateCancellationTemplateData = {
    body: CreateOrderCancellationReasonTemplateSchema;
    path?: never;
    query?: never;
    url: '/api/orders/cancellation-templates/';
};

export type OrdersApisCreateCancellationTemplateResponses = {
    /**
     * OK
     */
    200: OrderCancellationReasonTemplateSchema;
};

export type OrdersApisCreateCancellationTemplateResponse = OrdersApisCreateCancellationTemplateResponses[keyof OrdersApisCreateCancellationTemplateResponses];

export type OrdersApisDeleteCancellationTemplateData = {
    body?: never;
    path: {
        /**
         * Template Id
         */
        template_id: string;
    };
    query?: never;
    url: '/api/orders/cancellation-templates/{template_id}';
};

export type OrdersApisDeleteCancellationTemplateResponses = {
    /**
     * Response
     * OK
     */
    200: string;
};

export type OrdersApisDeleteCancellationTemplateResponse = OrdersApisDeleteCancellationTemplateResponses[keyof OrdersApisDeleteCancellationTemplateResponses];

export type OrdersApisGetCancellationTemplateData = {
    body?: never;
    path: {
        /**
         * Template Id
         */
        template_id: string;
    };
    query?: never;
    url: '/api/orders/cancellation-templates/{template_id}';
};

export type OrdersApisGetCancellationTemplateResponses = {
    /**
     * OK
     */
    200: OrderCancellationReasonTemplateSchema;
};

export type OrdersApisGetCancellationTemplateResponse = OrdersApisGetCancellationTemplateResponses[keyof OrdersApisGetCancellationTemplateResponses];

export type OrdersApisUpdateCancellationTemplateData = {
    body: UpdateOrderCancellationReasonTemplateSchema;
    path: {
        /**
         * Template Id
         */
        template_id: string;
    };
    query?: never;
    url: '/api/orders/cancellation-templates/{template_id}';
};

export type OrdersApisUpdateCancellationTemplateResponses = {
    /**
     * OK
     */
    200: OrderCancellationReasonTemplateSchema;
};

export type OrdersApisUpdateCancellationTemplateResponse = OrdersApisUpdateCancellationTemplateResponses[keyof OrdersApisUpdateCancellationTemplateResponses];

export type OrdersApisListOrdersData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Page
         */
        page?: number;
        /**
         * Page Size
         */
        page_size?: number;
        /**
         * Office Id
         */
        office_id?: string;
        /**
         * Status
         */
        status?: string;
        /**
         * Assigned To Id
         */
        assigned_to_id?: string;
        /**
         * Customer Company Id
         */
        customer_company_id?: string;
        /**
         * Search
         */
        search?: string;
        /**
         * Created At From
         */
        created_at_from?: string;
        /**
         * Created At To
         */
        created_at_to?: string;
        /**
         * Assigned To Isnull
         */
        assigned_to_isnull?: boolean;
    };
    url: '/api/orders/';
};

export type OrdersApisListOrdersResponses = {
    /**
     * OK
     */
    200: OrderListResponseSchema;
};

export type OrdersApisListOrdersResponse = OrdersApisListOrdersResponses[keyof OrdersApisListOrdersResponses];

export type OrdersApisCreateOrderData = {
    body: CreateOrderSchema;
    path?: never;
    query?: never;
    url: '/api/orders/';
};

export type OrdersApisCreateOrderResponses = {
    /**
     * OK
     */
    200: OrderSchema;
};

export type OrdersApisCreateOrderResponse = OrdersApisCreateOrderResponses[keyof OrdersApisCreateOrderResponses];

export type OrdersApisCreateOrdersBulkData = {
    /**
     * Data
     */
    body: Array<CreateOrderSchema>;
    path?: never;
    query?: never;
    url: '/api/orders/bulk';
};

export type OrdersApisCreateOrdersBulkResponses = {
    /**
     * OK
     */
    200: BulkCreateOrderResponseSchema;
};

export type OrdersApisCreateOrdersBulkResponse = OrdersApisCreateOrdersBulkResponses[keyof OrdersApisCreateOrdersBulkResponses];

export type OrdersApisDeleteOrderData = {
    body?: never;
    path: {
        /**
         * Order Id
         */
        order_id: string;
    };
    query?: never;
    url: '/api/orders/{order_id}';
};

export type OrdersApisDeleteOrderResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type OrdersApisGetOrderData = {
    body?: never;
    path: {
        /**
         * Order Id
         */
        order_id: string;
    };
    query?: never;
    url: '/api/orders/{order_id}';
};

export type OrdersApisGetOrderResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type OrdersApisUpdateOrderData = {
    body: UpdateOrderSchema;
    path: {
        /**
         * Order Id
         */
        order_id: string;
    };
    query?: never;
    url: '/api/orders/{order_id}';
};

export type OrdersApisUpdateOrderResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type OrdersApisListOrderStatusHistoryData = {
    body?: never;
    path: {
        /**
         * Order Id
         */
        order_id: string;
    };
    query?: {
        /**
         * Page
         */
        page?: number;
        /**
         * Page Size
         */
        page_size?: number;
    };
    url: '/api/orders/{order_id}/status-history/';
};

export type OrdersApisListOrderStatusHistoryResponses = {
    /**
     * OK
     */
    200: OrderStatusHistoryListResponseSchema;
};

export type OrdersApisListOrderStatusHistoryResponse = OrdersApisListOrderStatusHistoryResponses[keyof OrdersApisListOrderStatusHistoryResponses];

export type OrdersApisListOrderAssigneeHistoryData = {
    body?: never;
    path: {
        /**
         * Order Id
         */
        order_id: string;
    };
    query?: {
        /**
         * Page
         */
        page?: number;
        /**
         * Page Size
         */
        page_size?: number;
    };
    url: '/api/orders/{order_id}/assignee-history/';
};

export type OrdersApisListOrderAssigneeHistoryResponses = {
    /**
     * OK
     */
    200: OrderAssigneeHistoryListResponseSchema;
};

export type OrdersApisListOrderAssigneeHistoryResponse = OrdersApisListOrderAssigneeHistoryResponses[keyof OrdersApisListOrderAssigneeHistoryResponses];

export type ClientOptions = {
    baseUrl: string;
};