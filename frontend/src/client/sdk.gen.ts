// This file is auto-generated by @hey-api/openapi-ts

import type { Options as ClientOptions, TDataShape, Client } from './client';
import type { AccountsApisLoginData, AccountsApisLoginResponses, AccountsApisLogoutData, AccountsApisLogoutResponses, AccountsApisGetMeData, AccountsApisGetMeResponses, AccountsApisListUsersData, AccountsApisListUsersResponses, AccountsApisCreateUserData, AccountsApisCreateUserResponses, AccountsApisDeleteUserData, AccountsApisDeleteUserResponses, AccountsApisGetUserData, AccountsApisGetUserResponses, AccountsApisUpdateUserData, AccountsApisUpdateUserResponses, AccountsApisUpdateLocationData, AccountsApisUpdateLocationResponses, AccountsApisGetLocationHistoryData, AccountsApisGetLocationHistoryResponses, AccountsApisGetMyOfficesData, AccountsApisGetMyOfficesResponses, AccountsApisGetMeWalletData, AccountsApisGetMeWalletResponses, AccountsApisUpdateMeWalletData, AccountsApisUpdateMeWalletResponses, AccountsApisGetMePointsData, AccountsApisGetMePointsResponses, AccountsApisAddPointsData, AccountsApisAddPointsResponses, MerchantsApisListOfficesData, MerchantsApisListOfficesResponses, MerchantsApisCreateOfficeData, MerchantsApisCreateOfficeResponses, MerchantsApisDeleteOfficeData, MerchantsApisDeleteOfficeResponses, MerchantsApisGetOfficeData, MerchantsApisGetOfficeResponses, MerchantsApisUpdateOfficeData, MerchantsApisUpdateOfficeResponses, MerchantsApisListEmployeesOfficesByPhoneNumberData, MerchantsApisListEmployeesOfficesByPhoneNumberResponses, MerchantsApisListOfficeEmployeesData, MerchantsApisListOfficeEmployeesResponses, MerchantsApisAddOfficeEmployeeData, MerchantsApisAddOfficeEmployeeResponses, MerchantsApisRemoveOfficeEmployeeData, MerchantsApisRemoveOfficeEmployeeResponses, OrdersApisListCompaniesData, OrdersApisListCompaniesResponses, OrdersApisCreateCompanyData, OrdersApisCreateCompanyResponses, OrdersApisDeleteCompanyData, OrdersApisDeleteCompanyResponses, OrdersApisGetCompanyData, OrdersApisGetCompanyResponses, OrdersApisUpdateCompanyData, OrdersApisUpdateCompanyResponses, OrdersApisListCompanyChannelsData, OrdersApisListCompanyChannelsResponses, OrdersApisCreateCompanyChannelData, OrdersApisCreateCompanyChannelResponses, OrdersApisDeleteCompanyChannelData, OrdersApisDeleteCompanyChannelResponses, OrdersApisGetCompanyChannelData, OrdersApisGetCompanyChannelResponses, OrdersApisUpdateCompanyChannelData, OrdersApisUpdateCompanyChannelResponses, OrdersApisListCancellationTemplatesData, OrdersApisListCancellationTemplatesResponses, OrdersApisCreateCancellationTemplateData, OrdersApisCreateCancellationTemplateResponses, OrdersApisDeleteCancellationTemplateData, OrdersApisDeleteCancellationTemplateResponses, OrdersApisGetCancellationTemplateData, OrdersApisGetCancellationTemplateResponses, OrdersApisUpdateCancellationTemplateData, OrdersApisUpdateCancellationTemplateResponses, OrdersApisListOrdersData, OrdersApisListOrdersResponses, OrdersApisCreateOrderData, OrdersApisCreateOrderResponses, OrdersApisCreateOrdersBulkData, OrdersApisCreateOrdersBulkResponses, OrdersApisDeleteOrderData, OrdersApisDeleteOrderResponses, OrdersApisGetOrderData, OrdersApisGetOrderResponses, OrdersApisUpdateOrderData, OrdersApisUpdateOrderResponses, OrdersApisListOrderStatusHistoryData, OrdersApisListOrderStatusHistoryResponses, OrdersApisListOrderAssigneeHistoryData, OrdersApisListOrderAssigneeHistoryResponses } from './types.gen';
import { client as _heyApiClient } from './client.gen';

export type Options<TData extends TDataShape = TDataShape, ThrowOnError extends boolean = boolean> = ClientOptions<TData, ThrowOnError> & {
    /**
     * You can provide a client instance returned by `createClient()` instead of
     * individual options. This might be also useful if you want to implement a
     * custom client.
     */
    client?: Client;
    /**
     * You can pass arbitrary values through the `meta` object. This can be
     * used to access values that aren't defined as part of the SDK function.
     */
    meta?: Record<string, unknown>;
};

/**
 * Login
 */
export const accountsApisLogin = <ThrowOnError extends boolean = false>(options: Options<AccountsApisLoginData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<AccountsApisLoginResponses, unknown, ThrowOnError>({
        url: '/api/accounts/login',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Logout
 */
export const accountsApisLogout = <ThrowOnError extends boolean = false>(options?: Options<AccountsApisLogoutData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<AccountsApisLogoutResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/accounts/logout',
        ...options
    });
};

/**
 * Get Me
 */
export const accountsApisGetMe = <ThrowOnError extends boolean = false>(options?: Options<AccountsApisGetMeData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<AccountsApisGetMeResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/accounts/me',
        ...options
    });
};

/**
 * List Users
 */
export const accountsApisListUsers = <ThrowOnError extends boolean = false>(options?: Options<AccountsApisListUsersData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<AccountsApisListUsersResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/accounts/',
        ...options
    });
};

/**
 * Create User
 */
export const accountsApisCreateUser = <ThrowOnError extends boolean = false>(options: Options<AccountsApisCreateUserData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<AccountsApisCreateUserResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/accounts/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Delete User
 */
export const accountsApisDeleteUser = <ThrowOnError extends boolean = false>(options: Options<AccountsApisDeleteUserData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<AccountsApisDeleteUserResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/accounts/{user_id}',
        ...options
    });
};

/**
 * Get User
 */
export const accountsApisGetUser = <ThrowOnError extends boolean = false>(options: Options<AccountsApisGetUserData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<AccountsApisGetUserResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/accounts/{user_id}',
        ...options
    });
};

/**
 * Update User
 */
export const accountsApisUpdateUser = <ThrowOnError extends boolean = false>(options: Options<AccountsApisUpdateUserData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<AccountsApisUpdateUserResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/accounts/{user_id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Update Location
 */
export const accountsApisUpdateLocation = <ThrowOnError extends boolean = false>(options: Options<AccountsApisUpdateLocationData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<AccountsApisUpdateLocationResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/accounts/me/location',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get Location History
 */
export const accountsApisGetLocationHistory = <ThrowOnError extends boolean = false>(options?: Options<AccountsApisGetLocationHistoryData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<AccountsApisGetLocationHistoryResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/accounts/me/location/history',
        ...options
    });
};

/**
 * Get My Offices
 * Get all offices where the current user is an employee
 */
export const accountsApisGetMyOffices = <ThrowOnError extends boolean = false>(options?: Options<AccountsApisGetMyOfficesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<AccountsApisGetMyOfficesResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/accounts/me/offices',
        ...options
    });
};

/**
 * Get Me Wallet
 */
export const accountsApisGetMeWallet = <ThrowOnError extends boolean = false>(options?: Options<AccountsApisGetMeWalletData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<AccountsApisGetMeWalletResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/accounts/me/wallet',
        ...options
    });
};

/**
 * Update Me Wallet
 */
export const accountsApisUpdateMeWallet = <ThrowOnError extends boolean = false>(options: Options<AccountsApisUpdateMeWalletData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<AccountsApisUpdateMeWalletResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/accounts/me/wallet',
        ...options
    });
};

/**
 * Get Me Points
 */
export const accountsApisGetMePoints = <ThrowOnError extends boolean = false>(options?: Options<AccountsApisGetMePointsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<AccountsApisGetMePointsResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/accounts/me/points',
        ...options
    });
};

/**
 * Add Points
 */
export const accountsApisAddPoints = <ThrowOnError extends boolean = false>(options: Options<AccountsApisAddPointsData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<AccountsApisAddPointsResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/accounts/me/points',
        ...options
    });
};

/**
 * List Offices
 */
export const merchantsApisListOffices = <ThrowOnError extends boolean = false>(options?: Options<MerchantsApisListOfficesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<MerchantsApisListOfficesResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/merchants/offices/',
        ...options
    });
};

/**
 * Create Office
 */
export const merchantsApisCreateOffice = <ThrowOnError extends boolean = false>(options: Options<MerchantsApisCreateOfficeData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<MerchantsApisCreateOfficeResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/merchants/offices/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Delete Office
 */
export const merchantsApisDeleteOffice = <ThrowOnError extends boolean = false>(options: Options<MerchantsApisDeleteOfficeData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<MerchantsApisDeleteOfficeResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/merchants/offices/{office_id}',
        ...options
    });
};

/**
 * Get Office
 */
export const merchantsApisGetOffice = <ThrowOnError extends boolean = false>(options: Options<MerchantsApisGetOfficeData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<MerchantsApisGetOfficeResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/merchants/offices/{office_id}',
        ...options
    });
};

/**
 * Update Office
 */
export const merchantsApisUpdateOffice = <ThrowOnError extends boolean = false>(options: Options<MerchantsApisUpdateOfficeData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<MerchantsApisUpdateOfficeResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/merchants/offices/{office_id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * List Employees Offices By Phone Number
 */
export const merchantsApisListEmployeesOfficesByPhoneNumber = <ThrowOnError extends boolean = false>(options: Options<MerchantsApisListEmployeesOfficesByPhoneNumberData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<MerchantsApisListEmployeesOfficesByPhoneNumberResponses, unknown, ThrowOnError>({
        url: '/api/merchants/offices/get-by-phone-number/',
        ...options
    });
};

/**
 * List Office Employees
 */
export const merchantsApisListOfficeEmployees = <ThrowOnError extends boolean = false>(options: Options<MerchantsApisListOfficeEmployeesData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<MerchantsApisListOfficeEmployeesResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/merchants/offices/{office_id}/employees/',
        ...options
    });
};

/**
 * Add Office Employee
 */
export const merchantsApisAddOfficeEmployee = <ThrowOnError extends boolean = false>(options: Options<MerchantsApisAddOfficeEmployeeData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<MerchantsApisAddOfficeEmployeeResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/merchants/offices/{office_id}/employees/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Remove Office Employee
 */
export const merchantsApisRemoveOfficeEmployee = <ThrowOnError extends boolean = false>(options: Options<MerchantsApisRemoveOfficeEmployeeData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<MerchantsApisRemoveOfficeEmployeeResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/merchants/offices/{office_id}/employees/{employee_id}',
        ...options
    });
};

/**
 * List Companies
 */
export const ordersApisListCompanies = <ThrowOnError extends boolean = false>(options?: Options<OrdersApisListCompaniesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<OrdersApisListCompaniesResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/companies/',
        ...options
    });
};

/**
 * Create Company
 */
export const ordersApisCreateCompany = <ThrowOnError extends boolean = false>(options: Options<OrdersApisCreateCompanyData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<OrdersApisCreateCompanyResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/companies/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Delete Company
 */
export const ordersApisDeleteCompany = <ThrowOnError extends boolean = false>(options: Options<OrdersApisDeleteCompanyData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<OrdersApisDeleteCompanyResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/companies/{company_id}',
        ...options
    });
};

/**
 * Get Company
 */
export const ordersApisGetCompany = <ThrowOnError extends boolean = false>(options: Options<OrdersApisGetCompanyData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<OrdersApisGetCompanyResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/companies/{company_id}',
        ...options
    });
};

/**
 * Update Company
 */
export const ordersApisUpdateCompany = <ThrowOnError extends boolean = false>(options: Options<OrdersApisUpdateCompanyData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<OrdersApisUpdateCompanyResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/companies/{company_id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * List Company Channels
 */
export const ordersApisListCompanyChannels = <ThrowOnError extends boolean = false>(options?: Options<OrdersApisListCompanyChannelsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<OrdersApisListCompanyChannelsResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/company-channels/',
        ...options
    });
};

/**
 * Create Company Channel
 */
export const ordersApisCreateCompanyChannel = <ThrowOnError extends boolean = false>(options: Options<OrdersApisCreateCompanyChannelData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<OrdersApisCreateCompanyChannelResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/company-channels/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Delete Company Channel
 */
export const ordersApisDeleteCompanyChannel = <ThrowOnError extends boolean = false>(options: Options<OrdersApisDeleteCompanyChannelData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<OrdersApisDeleteCompanyChannelResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/company-channels/{channel_id}',
        ...options
    });
};

/**
 * Get Company Channel
 */
export const ordersApisGetCompanyChannel = <ThrowOnError extends boolean = false>(options: Options<OrdersApisGetCompanyChannelData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<OrdersApisGetCompanyChannelResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/company-channels/{channel_id}',
        ...options
    });
};

/**
 * Update Company Channel
 */
export const ordersApisUpdateCompanyChannel = <ThrowOnError extends boolean = false>(options: Options<OrdersApisUpdateCompanyChannelData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<OrdersApisUpdateCompanyChannelResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/company-channels/{channel_id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * List Cancellation Templates
 */
export const ordersApisListCancellationTemplates = <ThrowOnError extends boolean = false>(options?: Options<OrdersApisListCancellationTemplatesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<OrdersApisListCancellationTemplatesResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/cancellation-templates/',
        ...options
    });
};

/**
 * Create Cancellation Template
 */
export const ordersApisCreateCancellationTemplate = <ThrowOnError extends boolean = false>(options: Options<OrdersApisCreateCancellationTemplateData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<OrdersApisCreateCancellationTemplateResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/cancellation-templates/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Delete Cancellation Template
 */
export const ordersApisDeleteCancellationTemplate = <ThrowOnError extends boolean = false>(options: Options<OrdersApisDeleteCancellationTemplateData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<OrdersApisDeleteCancellationTemplateResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/cancellation-templates/{template_id}',
        ...options
    });
};

/**
 * Get Cancellation Template
 */
export const ordersApisGetCancellationTemplate = <ThrowOnError extends boolean = false>(options: Options<OrdersApisGetCancellationTemplateData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<OrdersApisGetCancellationTemplateResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/cancellation-templates/{template_id}',
        ...options
    });
};

/**
 * Update Cancellation Template
 */
export const ordersApisUpdateCancellationTemplate = <ThrowOnError extends boolean = false>(options: Options<OrdersApisUpdateCancellationTemplateData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<OrdersApisUpdateCancellationTemplateResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/cancellation-templates/{template_id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * List Orders
 */
export const ordersApisListOrders = <ThrowOnError extends boolean = false>(options?: Options<OrdersApisListOrdersData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<OrdersApisListOrdersResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/',
        ...options
    });
};

/**
 * Create Order
 */
export const ordersApisCreateOrder = <ThrowOnError extends boolean = false>(options: Options<OrdersApisCreateOrderData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<OrdersApisCreateOrderResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Create Orders Bulk
 * Create multiple orders in bulk with transaction support and comprehensive error handling.
 *
 * This endpoint processes multiple orders atomically - either all orders are created successfully
 * or none are created if any validation fails. Each order is validated individually and
 * detailed error information is provided for failed orders.
 */
export const ordersApisCreateOrdersBulk = <ThrowOnError extends boolean = false>(options: Options<OrdersApisCreateOrdersBulkData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<OrdersApisCreateOrdersBulkResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/bulk',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Delete Order
 */
export const ordersApisDeleteOrder = <ThrowOnError extends boolean = false>(options: Options<OrdersApisDeleteOrderData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<OrdersApisDeleteOrderResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/{order_id}',
        ...options
    });
};

/**
 * Get Order
 */
export const ordersApisGetOrder = <ThrowOnError extends boolean = false>(options: Options<OrdersApisGetOrderData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<OrdersApisGetOrderResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/{order_id}',
        ...options
    });
};

/**
 * Update Order
 */
export const ordersApisUpdateOrder = <ThrowOnError extends boolean = false>(options: Options<OrdersApisUpdateOrderData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<OrdersApisUpdateOrderResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/{order_id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * List Order Status History
 */
export const ordersApisListOrderStatusHistory = <ThrowOnError extends boolean = false>(options: Options<OrdersApisListOrderStatusHistoryData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<OrdersApisListOrderStatusHistoryResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/{order_id}/status-history/',
        ...options
    });
};

/**
 * List Order Assignee History
 */
export const ordersApisListOrderAssigneeHistory = <ThrowOnError extends boolean = false>(options: Options<OrdersApisListOrderAssigneeHistoryData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<OrdersApisListOrderAssigneeHistoryResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/orders/{order_id}/assignee-history/',
        ...options
    });
};