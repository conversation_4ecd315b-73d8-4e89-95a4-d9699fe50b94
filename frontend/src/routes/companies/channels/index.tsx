import { SiteHeader } from '@/components/site-header'
import { createFileRoute, useNavigate } from '@tanstack/react-router'
import { Separator } from '@/components/ui/separator'
import { useAuth } from '@/contexts/AuthContext'
import { useEffect, useState } from 'react'
import { ordersApisListCompanyChannels, ordersApisDeleteCompanyChannel, type CompanyChannelSchema } from '@/client'
import { DataTableComponent } from '@/components/DataTableComponent'
import { toast } from 'sonner'

export const Route = createFileRoute('/companies/channels/')({
    component: RouteComponent,
    validateSearch: (search: Record<string, unknown>) => ({
        company_id: search.company_id as string | undefined,
    }),
})

function RouteComponent() {
    const { selectedOffice } = useAuth()
    const { company_id } = Route.useSearch()
    const [data, setData] = useState<CompanyChannelSchema[]>([])
    const navigate = useNavigate()

    const fetchData = async () => {
        try {
            const response = await ordersApisListCompanyChannels({
                query: {
                    office_id: selectedOffice!.id,
                    company_id: company_id,
                },
            })
            setData(response.data?.channels || [])
        } catch (error) {
            console.error('Error fetching channels:', error)
            toast.error('حدث خطأ أثناء تحميل بيانات القنوات')
        }
    }

    useEffect(() => {
        fetchData()
    }, [selectedOffice, company_id])

    const handleDeleteChannel = async (channel: CompanyChannelSchema) => {
        if (confirm('هل أنت متأكد من حذف هذه القناة؟')) {
            try {
                await ordersApisDeleteCompanyChannel({
                    path: {
                        channel_id: channel.id,
                    },
                })

                toast.success('تم حذف القناة بنجاح')
                fetchData() // Refresh the data
            } catch (error) {
                console.error('Error deleting channel:', error)
                toast.error('حدث خطأ أثناء حذف القناة')
            }
        }
    }

    return <>
        <SiteHeader title="قنوات الشركة" />
        <div className="flex flex-col m-4">
            <h1 className="text-2xl font-bold">قنوات الشركة</h1>
            <p className="text-sm text-muted-foreground">هنا يمكنك إدارة قنوات الشركة</p>
        </div>
        <Separator className="mb-4" />
        <DataTableComponent
            idField='id'
            pageSize={10}
            enablePagination={false}
            showAddButton={true}
            addButtonText="إضافة قناة"
            onAddClick={() => {
                navigate({
                    to: "/companies/channels/form",
                    search: { id: undefined, company_id }
                })
            }}
            data={data}
            columns={
                [
                    {
                        header: "اسم القناة",
                        accessorKey: "name",
                    },
                    {
                        header: "رقم الواتساب",
                        accessorKey: "channel_whatsapp_number",
                    },
                    {
                        header: "الملاحظات",
                        accessorKey: "notes",
                    },
                ]
            }
            actionsColumn={{
                enableEdit: false,
                enableDelete: false,
                customActions: [
                    {
                        label: "تعديل",
                        onClick: (row) => {
                            navigate({
                                to: "/companies/channels/form",
                                search: { id: row.id, company_id }
                            })
                        }
                    },
                    {
                        label: "حذف",
                        onClick: handleDeleteChannel,
                        variant: "destructive",
                    },
                ]
            }}
        />
    </>
}
