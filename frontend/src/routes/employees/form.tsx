import { SiteHeader } from '@/components/site-header'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { createFileRoute, useNavigate, useSearch } from '@tanstack/react-router'
import { useState, useEffect } from 'react'
import { accountsApisCreateUser, accountsApisGetUser, accountsApisUpdateUser, merchantsApisAddOfficeEmployee, merchantsApisListEmployeesOfficesByPhoneNumber, type OfficeSchema } from '@/client'
import { toast } from 'sonner'
import { useAuth } from '@/contexts/AuthContext'

export const Route = createFileRoute('/employees/form')({
    component: RouteComponent,
    validateSearch: (search: Record<string, unknown>) => ({
        id: search.id as string | undefined,
    }),
})

interface FormData {
    first_name: string
    last_name: string
    email: string
    phone_number: string
    role: string
    commission_fixed_rate: string
    password: string
    confirm_password: string
}

interface FormErrors {
    [key: string]: string
}

function RouteComponent() {
    // get query params
    const { id } = Route.useSearch()
    const { selectedOffice } = useAuth()
    const navigate = useNavigate()
    const [step, setStep] = useState(1)
    const [loading, setLoading] = useState(false)
    const [isEditMode, setIsEditMode] = useState(false)
    const [selectedOfficeId, setSelectedOfficeId] = useState<string>('')
    const [formData, setFormData] = useState<FormData>({
        first_name: '',
        last_name: '',
        email: '',
        phone_number: '',
        role: '',
        commission_fixed_rate: '',
        password: '',
        confirm_password: '',
    })
    const [errors, setErrors] = useState<FormErrors>({})
    const [offices, setOffices] = useState<OfficeSchema[]>([])

    // Load user data if in edit mode
    useEffect(() => {
        if (id) {
            setIsEditMode(true)
            getUserData()
        }
    }, [id])

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target
        setFormData(prev => ({ ...prev, [name]: value }))
        // Clear error when user starts typing
        if (errors[name]) {
            setErrors(prev => ({ ...prev, [name]: '' }))
        }
    }

    const handleSelectChange = (name: string, value: string) => {
        setFormData(prev => ({ ...prev, [name]: value }))
        if (errors[name]) {
            setErrors(prev => ({ ...prev, [name]: '' }))
        }
    }

    const validateStep1 = (): boolean => {
        const newErrors: FormErrors = {}

        if (!formData.first_name.trim()) {
            newErrors.first_name = 'الاسم الأول مطلوب'
        }
        if (!formData.last_name.trim()) {
            newErrors.last_name = 'الاسم الأخير مطلوب'
        }
        if (!formData.email.trim()) {
            newErrors.email = 'البريد الإلكتروني مطلوب'
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
            newErrors.email = 'البريد الإلكتروني غير صحيح'
        }
        if (!formData.phone_number.trim()) {
            newErrors.phone_number = 'رقم الهاتف مطلوب'
        }
        if (!formData.role) {
            newErrors.role = 'المنصب مطلوب'
        }
        if (!formData.commission_fixed_rate) {
            newErrors.commission_fixed_rate = 'نسبة العمولة مطلوبة'
        } else if (isNaN(Number(formData.commission_fixed_rate)) || Number(formData.commission_fixed_rate) < 0) {
            newErrors.commission_fixed_rate = 'نسبة العمولة يجب أن تكون رقم موجب'
        }

        // Only validate password fields if not in edit mode or if password is provided
        if (!isEditMode) {
            if (!formData.password) {
                newErrors.password = 'كلمة المرور مطلوبة'
            } else if (formData.password.length < 8) {
                newErrors.password = 'كلمة المرور يجب أن تكون 8 أحرف على الأقل'
            }
            if (!formData.confirm_password) {
                newErrors.confirm_password = 'تأكيد كلمة المرور مطلوب'
            } else if (formData.password !== formData.confirm_password) {
                newErrors.confirm_password = 'كلمة المرور غير متطابقة'
            }
        } else {
            // In edit mode, validate password only if provided
            if (formData.password && formData.password.length < 8) {
                newErrors.password = 'كلمة المرور يجب أن تكون 8 أحرف على الأقل'
            }
            if (formData.password && !formData.confirm_password) {
                newErrors.confirm_password = 'تأكيد كلمة المرور مطلوب'
            } else if (formData.password && formData.password !== formData.confirm_password) {
                newErrors.confirm_password = 'كلمة المرور غير متطابقة'
            }
        }

        setErrors(newErrors)
        return Object.keys(newErrors).length === 0
    }

    const handleStep1Submit = async (e: React.FormEvent) => {
        e.preventDefault()

        if (!validateStep1()) {
            toast.error('يرجى تصحيح الأخطاء في النموذج')
            return
        }

        // If in edit mode, update the user directly
        if (isEditMode) {
            setLoading(true)
            try {
                await updateUserData()
            } catch (error) {
                console.error('Error updating user:', error)
                toast.error('حدث خطأ أثناء تحديث الموظف')
            } finally {
                setLoading(false)
            }
            return
        }

        setLoading(true)
        try {
            const res = await merchantsApisListEmployeesOfficesByPhoneNumber({
                query: {
                    employee_phone_number: formData.phone_number,
                },
            })

            const officesList = res.data?.offices ?? []
            setOffices(officesList)

            if (officesList.length > 0) {
                setStep(2)
                toast.info('تم العثور على مكاتب مرتبطة بهذا الرقم، يرجى اختيار المكتب')
            } else {
                await createEmployee(selectedOffice!.id)
            }
        } catch (error) {
            console.error('Error checking employee offices:', error)
            toast.error('حدث خطأ أثناء التحقق من المكاتب')
        } finally {
            setLoading(false)
        }
    }

    const handleStep2Submit = async (e: React.FormEvent) => {
        e.preventDefault()

        if (!selectedOfficeId) {
            toast.error('يرجى اختيار مكتب')
            return
        }

        setLoading(true)
        try {
            await createEmployee(selectedOfficeId)
        } catch (error) {
            console.error('Error creating employee:', error)
            toast.error('حدث خطأ أثناء إنشاء الموظف')
        } finally {
            setLoading(false)
        }
    }

    const createEmployee = async (officeId: string) => {
        try {
            // Create user account
            const userRes = await accountsApisCreateUser({
                body: {
                    first_name: formData.first_name,
                    last_name: formData.last_name,
                    email: formData.email,
                    username: formData.email.split('@')[0] + '_' + formData.phone_number,
                    phone_number: formData.phone_number,
                    office_id: officeId,
                    role: formData.role,
                    commission_fixed_rate: Number(formData.commission_fixed_rate),
                    password: formData.password,
                },
            })

            if (!userRes.data) {
                throw new Error('Failed to create user')
            }

            // Add employee to office
            // const employeeRes = await merchantsApisAddOfficeEmployee({
            //     path: {
            //         office_id: officeId,
            //     },
            //     body: {
            //         user_id: userRes.data.id,
            //         office_id: officeId,
            //     },
            // })

            if (userRes.data) {
                toast.success('تم إنشاء الموظف بنجاح')
                navigate({ to: '/employees' })
            } else {
                throw new Error('Failed to add employee to office')
            }
        } catch (error) {
            console.error('Error in createEmployee:', error)
            throw error
        }
    }

    const handleCancel = () => {
        navigate({ to: '/employees' })
    }

    const getStepStatus = (stepNumber: number) => {
        if (stepNumber < step) return 'completed'
        if (stepNumber === step) return 'current'
        return 'pending'
    }

    const getUserData = async () => {
        if (!id) return
        try {
            const res = await accountsApisGetUser({
                path: {
                    user_id: id,
                },
            })
            if (res.data) {
                setFormData({
                    first_name: res.data.first_name,
                    last_name: res.data.last_name,
                    email: res.data.email,
                    phone_number: res.data.phone_number,
                    role: res.data.role,
                    commission_fixed_rate: res.data.commission_fixed_rate.toString(),
                    password: '',
                    confirm_password: '',
                })
            }
        } catch (error) {
            console.error('Error fetching user data:', error)
            toast.error('حدث خطأ أثناء تحميل بيانات الموظف')
        }
    }

    const updateUserData = async () => {
        if (!id) return
        const updateData: any = {
            first_name: formData.first_name,
            last_name: formData.last_name,
            email: formData.email,
            phone_number: formData.phone_number,
            role: formData.role,
            commission_fixed_rate: Number(formData.commission_fixed_rate),
            username: formData.email.split('@')[0] + '_' + formData.phone_number,
            current_location_lat: 0,
            current_location_lng: 0,
        }

        // Only include password if it's provided
        if (formData.password) {
            updateData.password = formData.password
        }

        const res = await accountsApisUpdateUser({
            path: {
                user_id: id,
            },
            body: updateData,
        })
        if (res.data) {
            toast.success('تم تحديث المعلومات بنجاح')
            navigate({ to: '/employees' })
        } else {
            throw new Error('Failed to update user data')
        }
    }

    const renderStepIndicator = (stepNumber: number, title: string, description: string) => {
        const status = getStepStatus(stepNumber)
        const isCompleted = status === 'completed'
        const isCurrent = status === 'current'

        return (
            <div className="flex items-center space-x-3">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${isCompleted ? 'bg-green-100' : isCurrent ? 'bg-blue-100' : 'bg-gray-100'
                    }`}>
                    <span className={`text-sm font-medium ${isCompleted ? 'text-green-600' : isCurrent ? 'text-blue-600' : 'text-gray-400'
                        }`}>
                        {isCompleted ? '✓' : stepNumber}
                    </span>
                </div>
                <div>
                    <p className={`text-sm font-medium ${isCompleted ? 'text-green-900' : isCurrent ? 'text-blue-900' : 'text-gray-400'
                        }`}>
                        {title}
                    </p>
                    <p className={`text-xs ${isCompleted ? 'text-green-700' : isCurrent ? 'text-blue-700' : 'text-gray-400'
                        }`}>
                        {description}
                    </p>
                </div>
            </div>
        )
    }

    return (
        <>
            <SiteHeader title={"الموظفين"} />
            <div className="flex min-h-screen pt-16">
                {/* Left Sidebar */}
                <div className="hidden lg:flex lg:w-80">
                    <div className="w-full p-8 pl-0">
                        <div className="space-y-6">
                            <div>
                                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                                    {isEditMode ? "تعديل موظف" : "إضافة موظف جديد"}
                                </h2>
                                <p className="text-gray-600 text-sm">
                                    {isEditMode
                                        ? "قم بتعديل معلومات الموظف أدناه"
                                        : "املأ النموذج أدناه لإضافة موظف جديد للنظام"
                                    }
                                </p>
                            </div>

                            <Separator />

                            {!isEditMode && (
                                <div className="space-y-4">
                                    {renderStepIndicator(1, 'المعلومات الأساسية', 'الاسم والبريد الإلكتروني')}
                                    {renderStepIndicator(2, 'معلومات الاتصال', 'رقم الهاتف والمنصب')}
                                    {renderStepIndicator(3, 'إعدادات الحساب', 'كلمة المرور والصلاحيات')}
                                </div>
                            )}

                            <Separator />

                            <div className="bg-blue-50 rounded-lg p-4">
                                <h4 className="text-sm font-medium text-blue-900 mb-2">💡 نصائح</h4>
                                <ul className="text-xs text-blue-800 space-y-1">
                                    <li>• تأكد من صحة البريد الإلكتروني</li>
                                    <li>• استخدم كلمة مرور قوية</li>
                                    <li>• حدد الصلاحيات المناسبة</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Main Content */}
                <div className="flex-1 p-4">
                    <div className="max-w-3xl mx-auto">
                        {/* Mobile Header */}
                        <div className="lg:hidden mb-8">
                            <h1 className="text-2xl font-bold text-gray-900 mb-2">
                                {isEditMode ? "تعديل موظف" : "إضافة موظف جديد"}
                            </h1>
                            <p className="text-gray-600">
                                {isEditMode
                                    ? "قم بتعديل معلومات الموظف أدناه"
                                    : "املأ النموذج أدناه لإضافة موظف جديد للنظام"
                                }
                            </p>
                        </div>

                        <form className="space-y-5" onSubmit={step === 1 ? handleStep1Submit : handleStep2Submit}>
                            {/* Step 1: Basic Information */}
                            {step === 1 && (
                                <>
                                    <Card>
                                        <CardHeader className="pb-4">
                                            <div className="flex items-center space-x-3">
                                                <Badge variant="secondary" className="bg-blue-100 text-blue-700">الخطوة 1</Badge>
                                                <CardTitle className="text-lg">المعلومات الأساسية</CardTitle>
                                            </div>
                                            <CardDescription>أدخل المعلومات الأساسية للموظف</CardDescription>
                                        </CardHeader>
                                        <CardContent className="space-y-6">
                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                                <div className="space-y-3">
                                                    <Label className="text-sm font-semibold text-gray-700 flex items-center">
                                                        <span className="w-2 h-2 bg-red-500 rounded-full mr-2"></span>
                                                        الاسم الأول
                                                    </Label>
                                                    <Input
                                                        name="first_name"
                                                        value={formData.first_name}
                                                        onChange={handleInputChange}
                                                        placeholder="أدخل الاسم الأول"
                                                        className={`h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200 ${errors.first_name ? 'border-red-500' : ''
                                                            }`}
                                                    />
                                                    {errors.first_name && (
                                                        <p className="text-red-500 text-xs">{errors.first_name}</p>
                                                    )}
                                                </div>
                                                <div className="space-y-3">
                                                    <Label className="text-sm font-semibold text-gray-700 flex items-center">
                                                        <span className="w-2 h-2 bg-red-500 rounded-full mr-2"></span>
                                                        الاسم الأخير
                                                    </Label>
                                                    <Input
                                                        name="last_name"
                                                        value={formData.last_name}
                                                        onChange={handleInputChange}
                                                        placeholder="أدخل الاسم الأخير"
                                                        className={`h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200 ${errors.last_name ? 'border-red-500' : ''
                                                            }`}
                                                    />
                                                    {errors.last_name && (
                                                        <p className="text-red-500 text-xs">{errors.last_name}</p>
                                                    )}
                                                </div>
                                            </div>
                                            <div className="space-y-3">
                                                <Label className="text-sm font-semibold text-gray-700 flex items-center">
                                                    <span className="w-2 h-2 bg-red-500 rounded-full mr-2"></span>
                                                    البريد الإلكتروني
                                                </Label>
                                                <Input
                                                    name="email"
                                                    type="email"
                                                    value={formData.email}
                                                    onChange={handleInputChange}
                                                    placeholder="<EMAIL>"
                                                    className={`h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200 ${errors.email ? 'border-red-500' : ''
                                                        }`}
                                                />
                                                {errors.email && (
                                                    <p className="text-red-500 text-xs">{errors.email}</p>
                                                )}
                                            </div>
                                        </CardContent>
                                    </Card>

                                    {/* Contact Information Section */}
                                    <Card>
                                        <CardHeader className="pb-4">
                                            <div className="flex items-center space-x-3">
                                                <Badge variant="secondary" className="bg-green-100 text-green-700">الخطوة 2</Badge>
                                                <CardTitle className="text-lg">معلومات الاتصال</CardTitle>
                                            </div>
                                            <CardDescription>أدخل معلومات الاتصال والمنصب</CardDescription>
                                        </CardHeader>
                                        <CardContent className="space-y-6">
                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                                <div className="space-y-3">
                                                    <Label className="text-sm font-semibold text-gray-700 flex items-center">
                                                        <span className="w-2 h-2 bg-red-500 rounded-full mr-2"></span>
                                                        رقم الهاتف
                                                    </Label>
                                                    <Input
                                                        name="phone_number"
                                                        value={formData.phone_number}
                                                        onChange={handleInputChange}
                                                        placeholder="+966 5X XXX XXXX"
                                                        className={`h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200 ${errors.phone_number ? 'border-red-500' : ''
                                                            }`}
                                                    />
                                                    {errors.phone_number && (
                                                        <p className="text-red-500 text-xs">{errors.phone_number}</p>
                                                    )}
                                                </div>
                                                <div className="space-y-3">
                                                    <Label className="text-sm font-semibold text-gray-700 flex items-center">
                                                        <span className="w-2 h-2 bg-red-500 rounded-full mr-2"></span>
                                                        المنصب
                                                    </Label>
                                                    <Select value={formData.role} onValueChange={(value) => handleSelectChange('role', value)}>
                                                        <SelectTrigger className={`h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200 ${errors.role ? 'border-red-500' : ''
                                                            }`}>
                                                            <SelectValue placeholder="اختر المنصب" />
                                                        </SelectTrigger>
                                                        <SelectContent>
                                                            <SelectItem value="admin">👨‍💼 ادمن</SelectItem>
                                                            <SelectItem value="manager">👨‍💻 مدير</SelectItem>
                                                            <SelectItem value="employee">👤 موظف</SelectItem>
                                                        </SelectContent>
                                                    </Select>
                                                    {errors.role && (
                                                        <p className="text-red-500 text-xs">{errors.role}</p>
                                                    )}
                                                </div>
                                            </div>
                                            <div className="space-y-3">
                                                <Label className="text-sm font-semibold text-gray-700 flex items-center">
                                                    <span className="w-2 h-2 bg-red-500 rounded-full mr-2"></span>
                                                    نسبة العمولة الثابتة (%)
                                                </Label>
                                                <Input
                                                    name="commission_fixed_rate"
                                                    type="number"
                                                    value={formData.commission_fixed_rate}
                                                    onChange={handleInputChange}
                                                    placeholder="0.00"
                                                    className={`h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200 max-w-xs ${errors.commission_fixed_rate ? 'border-red-500' : ''
                                                        }`}
                                                />
                                                {errors.commission_fixed_rate && (
                                                    <p className="text-red-500 text-xs">{errors.commission_fixed_rate}</p>
                                                )}
                                            </div>
                                        </CardContent>
                                    </Card>

                                    {/* Account Settings Section */}
                                    <Card>
                                        <CardHeader className="pb-4">
                                            <div className="flex items-center space-x-3">
                                                <Badge variant="secondary" className="bg-purple-100 text-purple-700">الخطوة 3</Badge>
                                                <CardTitle className="text-lg">إعدادات الحساب</CardTitle>
                                            </div>
                                            <CardDescription>قم بإنشاء كلمة مرور آمنة للحساب</CardDescription>
                                        </CardHeader>
                                        <CardContent className="space-y-6">
                                            {isEditMode && (
                                                <div className="bg-blue-50 rounded-lg p-4 mb-4">
                                                    <p className="text-sm text-blue-800">
                                                        💡 <strong>ملاحظة:</strong> اترك حقول كلمة المرور فارغة إذا كنت لا تريد تغيير كلمة المرور الحالية
                                                    </p>
                                                </div>
                                            )}
                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                                <div className="space-y-3">
                                                    <Label className="text-sm font-semibold text-gray-700 flex items-center">
                                                        <span className={`w-2 h-2 rounded-full mr-2 ${isEditMode ? 'bg-gray-400' : 'bg-red-500'}`}></span>
                                                        كلمة المرور
                                                        {isEditMode && <span className="text-xs text-gray-500 mr-2">(اختياري)</span>}
                                                    </Label>
                                                    <Input
                                                        name="password"
                                                        type="password"
                                                        value={formData.password}
                                                        onChange={handleInputChange}
                                                        placeholder={isEditMode ? "اترك فارغاً إذا كنت لا تريد التغيير" : "أدخل كلمة مرور قوية"}
                                                        className={`h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200 ${errors.password ? 'border-red-500' : ''
                                                            }`}
                                                    />
                                                    {errors.password && (
                                                        <p className="text-red-500 text-xs">{errors.password}</p>
                                                    )}
                                                </div>
                                                <div className="space-y-3">
                                                    <Label className="text-sm font-semibold text-gray-700 flex items-center">
                                                        <span className={`w-2 h-2 rounded-full mr-2 ${isEditMode ? 'bg-gray-400' : 'bg-red-500'}`}></span>
                                                        تأكيد كلمة المرور
                                                        {isEditMode && <span className="text-xs text-gray-500 mr-2">(اختياري)</span>}
                                                    </Label>
                                                    <Input
                                                        name="confirm_password"
                                                        type="password"
                                                        value={formData.confirm_password}
                                                        onChange={handleInputChange}
                                                        placeholder={isEditMode ? "أعد إدخال كلمة المرور الجديدة" : "أعد إدخال كلمة المرور"}
                                                        className={`h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200 ${errors.confirm_password ? 'border-red-500' : ''
                                                            }`}
                                                    />
                                                    {errors.confirm_password && (
                                                        <p className="text-red-500 text-xs">{errors.confirm_password}</p>
                                                    )}
                                                </div>
                                            </div>
                                        </CardContent>
                                    </Card>
                                </>
                            )}

                            {/* Step 2: Office Selection */}
                            {step === 2 && (
                                <Card>
                                    <CardHeader>
                                        <CardTitle>اختر المكتب</CardTitle>
                                        <CardDescription>تم العثور على المكاتب التالية المرتبطة بهذا الرقم</CardDescription>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                            {offices.map((office) => (
                                                <Card
                                                    key={office.id}
                                                    className={`bg-card rounded-xl border py-6 shadow-sm cursor-pointer transition-all duration-200 ${selectedOfficeId === office.id
                                                        ? 'border-blue-500 bg-blue-50'
                                                        : 'hover:border-gray-300'
                                                        }`}
                                                    onClick={() => setSelectedOfficeId(office.id)}
                                                >
                                                    <CardHeader>
                                                        <CardTitle className="flex items-center justify-between">
                                                            {office.name}
                                                            {selectedOfficeId === office.id && (
                                                                <Badge variant="secondary" className="bg-blue-100 text-blue-700">
                                                                    مختار
                                                                </Badge>
                                                            )}
                                                        </CardTitle>
                                                        <CardDescription>{office.address}</CardDescription>
                                                    </CardHeader>
                                                    <CardContent>
                                                        <p className="text-sm text-gray-600">{office.phone_number}</p>
                                                    </CardContent>
                                                </Card>
                                            ))}
                                        </div>
                                    </CardContent>
                                </Card>
                            )}

                            {/* Action Buttons */}
                            <div className="flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-4 pt-6">
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={handleCancel}
                                    disabled={loading}
                                >
                                    إلغاء
                                </Button>
                                <Button
                                    type="submit"
                                    disabled={loading}
                                >
                                    {loading
                                        ? 'جاري المعالجة...'
                                        : isEditMode
                                            ? 'تحديث الموظف'
                                            : step === 1
                                                ? 'التالي'
                                                : 'إضافة الموظف'
                                    }
                                </Button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </>
    )
}
