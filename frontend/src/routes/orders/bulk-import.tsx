import { useState, useRef } from 'react'
import { createFileRoute, useNavigate } from '@tanstack/react-router'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import { Progress } from '@/components/ui/progress'
import { IconUpload, IconFileSpreadsheet, IconArrowLeft, IconFile, IconX, IconEye, IconArrowRight, IconLink, IconLoader2, IconCheck, IconAlertTriangle } from '@tabler/icons-react'
import * as XLSX from 'xlsx'
import Papa from 'papaparse'
import { toast } from 'sonner'
import Spreadsheet from 'react-spreadsheet'
import { ordersApisCreateOrder } from '@/client'
import { useAuth } from '@/contexts/AuthContext'
import type { CreateOrderSchema, OrderHandlingStatusSchema } from '@/client/types.gen'

export const Route = createFileRoute('/orders/bulk-import')({
    component: RouteComponent,
})

// Import workflow steps
enum ImportStep {
    UPLOAD = 'upload',
    PREVIEW = 'preview',
    MAPPING = 'mapping',
    PROCESSING = 'processing',
    RESULTS = 'results'
}

interface ImportState {
    step: ImportStep
    file: File | null
    data: any[][]
    headers: string[]
    mapping: Record<string, string>
    dataRange: {
        startRow: number
        startCol: number
        endRow: number
        endCol: number
    }
    results: {
        total: number
        successful: number
        failed: number
        errors: Array<{ row: number; data: any; error: string }>
    }
}

function RouteComponent() {
    const navigate = useNavigate()

    const [importState, setImportState] = useState<ImportState>({
        step: ImportStep.UPLOAD,
        file: null,
        data: [],
        headers: [],
        mapping: {},
        dataRange: {
            startRow: 0,
            startCol: 0,
            endRow: 0,
            endCol: 0
        },
        results: {
            total: 0,
            successful: 0,
            failed: 0,
            errors: []
        }
    })

    const handleBack = () => {
        navigate({ to: '/orders' })
    }

    const resetImport = () => {
        setImportState({
            step: ImportStep.UPLOAD,
            file: null,
            data: [],
            headers: [],
            mapping: {},
            dataRange: {
                startRow: 0,
                startCol: 0,
                endRow: 0,
                endCol: 0
            },
            results: {
                total: 0,
                successful: 0,
                failed: 0,
                errors: []
            }
        })
    }

    return (
        <div className="flex flex-col m-4 space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4 rtl:space-x-reverse">
                    <Button variant="outline" size="sm" onClick={handleBack}>
                        <IconArrowLeft className="h-4 w-4" />
                        العودة للطلبات
                    </Button>
                    <div>
                        <h1 className="text-2xl font-bold">استيراد الطلبات بالجملة</h1>
                        <p className="text-muted-foreground">
                            استيراد عدة طلبات من ملف Excel أو CSV
                        </p>
                    </div>
                </div>
                {importState.step !== ImportStep.UPLOAD && (
                    <Button variant="outline" onClick={resetImport}>
                        بدء جديد
                    </Button>
                )}
            </div>

            <Separator />

            {/* Progress Steps */}
            <div className="flex items-center justify-center space-x-4 rtl:space-x-reverse">
                {Object.values(ImportStep).map((step, index) => {
                    const stepNames = {
                        [ImportStep.UPLOAD]: 'رفع الملف',
                        [ImportStep.PREVIEW]: 'معاينة البيانات',
                        [ImportStep.MAPPING]: 'ربط الأعمدة',
                        [ImportStep.PROCESSING]: 'معالجة البيانات',
                        [ImportStep.RESULTS]: 'النتائج'
                    }

                    const isActive = importState.step === step
                    const isCompleted = Object.values(ImportStep).indexOf(importState.step) > index

                    return (
                        <div key={step} className="flex items-center">
                            <div className={`
                                flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium
                                ${isActive ? 'bg-primary text-primary-foreground' :
                                    isCompleted ? 'bg-green-500 text-white' :
                                        'bg-muted text-muted-foreground'}
                            `}>
                                {index + 1}
                            </div>
                            <span className={`ml-2 rtl:mr-2 text-sm ${isActive ? 'font-medium' : ''}`}>
                                {stepNames[step]}
                            </span>
                            {index < Object.values(ImportStep).length - 1 && (
                                <div className="w-8 h-px bg-muted mx-4" />
                            )}
                        </div>
                    )
                })}
            </div>

            {/* Main Content */}
            <div className="flex-1">
                {importState.step === ImportStep.UPLOAD && (
                    <FileUploadStep
                        importState={importState}
                        setImportState={setImportState}
                    />
                )}
                {importState.step === ImportStep.PREVIEW && (
                    <DataPreviewStep
                        importState={importState}
                        setImportState={setImportState}
                    />
                )}
                {importState.step === ImportStep.MAPPING && (
                    <ColumnMappingStep
                        importState={importState}
                        setImportState={setImportState}
                    />
                )}
                {importState.step === ImportStep.PROCESSING && (
                    <ProcessingStep
                        importState={importState}
                        setImportState={setImportState}
                    />
                )}
                {importState.step === ImportStep.RESULTS && (
                    <ResultsStep
                        importState={importState}
                        setImportState={setImportState}
                    />
                )}
            </div>
        </div>
    )
}

// File upload component
function FileUploadStep({
    importState,
    setImportState
}: {
    importState: ImportState,
    setImportState: React.Dispatch<React.SetStateAction<ImportState>>
}) {
    const fileInputRef = useRef<HTMLInputElement>(null)
    const [dragActive, setDragActive] = useState(false)
    const [uploading, setUploading] = useState(false)

    const handleFileSelect = (file: File) => {
        if (!file) return

        // Validate file type
        const validTypes = [
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
            'application/vnd.ms-excel', // .xls
            'text/csv', // .csv
            'application/csv'
        ]

        if (!validTypes.includes(file.type) && !file.name.match(/\.(xlsx|xls|csv)$/i)) {
            toast.error('نوع الملف غير مدعوم. يرجى اختيار ملف Excel (.xlsx, .xls) أو CSV')
            return
        }

        // Validate file size (max 10MB)
        if (file.size > 10 * 1024 * 1024) {
            toast.error('حجم الملف كبير جداً. الحد الأقصى 10 ميجابايت')
            return
        }

        setUploading(true)
        processFile(file)
    }

    const processFile = async (file: File) => {
        try {
            let data: any[][] = []
            let headers: string[] = []

            if (file.name.match(/\.(xlsx|xls)$/i)) {
                // Process Excel file
                const arrayBuffer = await file.arrayBuffer()
                const workbook = XLSX.read(arrayBuffer, { type: 'array' })
                const sheetName = workbook.SheetNames[0]
                const worksheet = workbook.Sheets[sheetName]
                const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })

                data = jsonData as any[][]
                headers = data[0]?.map(h => String(h || '')) || []

                // Remove empty rows
                data = data.filter(row => row.some(cell => cell !== null && cell !== undefined && cell !== ''))

            } else if (file.name.match(/\.csv$/i)) {
                // Process CSV file
                const text = await file.text()
                const result = Papa.parse(text, {
                    header: false,
                    skipEmptyLines: true,
                    encoding: 'UTF-8'
                })

                data = result.data as any[][]
                headers = data[0]?.map(h => String(h || '')) || []
            }

            if (data.length === 0) {
                toast.error('الملف فارغ أو لا يحتوي على بيانات صالحة')
                return
            }

            if (data.length === 1) {
                toast.error('الملف يحتوي على رؤوس الأعمدة فقط. يرجى إضافة بيانات الطلبات')
                return
            }

            // Update state with file data
            setImportState(prev => ({
                ...prev,
                file,
                data,
                headers,
                dataRange: {
                    startRow: 0,
                    startCol: 0,
                    endRow: data.length - 1,
                    endCol: headers.length - 1
                },
                step: ImportStep.PREVIEW
            }))

            toast.success(`تم تحميل الملف بنجاح. تم العثور على ${data.length - 1} صف من البيانات`)

        } catch (error) {
            console.error('Error processing file:', error)
            toast.error('حدث خطأ أثناء معالجة الملف. يرجى التأكد من صحة تنسيق الملف')
        } finally {
            setUploading(false)
        }
    }

    const handleDrag = (e: React.DragEvent) => {
        e.preventDefault()
        e.stopPropagation()
        if (e.type === 'dragenter' || e.type === 'dragover') {
            setDragActive(true)
        } else if (e.type === 'dragleave') {
            setDragActive(false)
        }
    }

    const handleDrop = (e: React.DragEvent) => {
        e.preventDefault()
        e.stopPropagation()
        setDragActive(false)

        if (e.dataTransfer.files && e.dataTransfer.files[0]) {
            handleFileSelect(e.dataTransfer.files[0])
        }
    }

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            handleFileSelect(e.target.files[0])
        }
    }

    const removeFile = () => {
        setImportState(prev => ({
            ...prev,
            file: null,
            data: [],
            headers: []
        }))
        if (fileInputRef.current) {
            fileInputRef.current.value = ''
        }
    }

    return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center space-x-2 rtl:space-x-reverse">
                    <IconFileSpreadsheet className="h-5 w-5" />
                    <span>رفع ملف البيانات</span>
                </CardTitle>
                <CardDescription>
                    اختر ملف Excel (.xlsx, .xls) أو CSV يحتوي على بيانات الطلبات
                </CardDescription>
            </CardHeader>
            <CardContent>
                {!importState.file ? (
                    <div
                        className={`
                            border-2 border-dashed rounded-lg p-8 text-center transition-colors
                            ${dragActive ? 'border-primary bg-primary/5' : 'border-muted-foreground/25'}
                            ${uploading ? 'opacity-50 pointer-events-none' : 'hover:border-primary hover:bg-primary/5'}
                        `}
                        onDragEnter={handleDrag}
                        onDragLeave={handleDrag}
                        onDragOver={handleDrag}
                        onDrop={handleDrop}
                    >
                        <IconUpload className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                        <h3 className="text-lg font-medium mb-2">
                            {uploading ? 'جاري معالجة الملف...' : 'اسحب الملف هنا أو انقر للاختيار'}
                        </h3>
                        <p className="text-muted-foreground mb-4">
                            الأنواع المدعومة: Excel (.xlsx, .xls) أو CSV
                        </p>
                        <p className="text-sm text-muted-foreground mb-6">
                            الحد الأقصى لحجم الملف: 10 ميجابايت
                        </p>
                        <Button
                            onClick={() => fileInputRef.current?.click()}
                            disabled={uploading}
                        >
                            اختيار ملف
                        </Button>
                        <Input
                            ref={fileInputRef}
                            type="file"
                            accept=".xlsx,.xls,.csv"
                            onChange={handleInputChange}
                            className="hidden"
                        />
                    </div>
                ) : (
                    <div className="space-y-4">
                        <div className="flex items-center justify-between p-4 border rounded-lg">
                            <div className="flex items-center space-x-3 rtl:space-x-reverse">
                                <IconFile className="h-8 w-8 text-primary" />
                                <div>
                                    <p className="font-medium">{importState.file.name}</p>
                                    <p className="text-sm text-muted-foreground">
                                        {(importState.file.size / 1024).toFixed(1)} كيلوبايت
                                    </p>
                                </div>
                            </div>
                            <Button variant="outline" size="sm" onClick={removeFile}>
                                <IconX className="h-4 w-4" />
                                إزالة
                            </Button>
                        </div>

                        <div className="flex justify-between items-center">
                            <p className="text-sm text-muted-foreground">
                                تم العثور على {importState.data.length - 1} صف من البيانات
                            </p>
                            <Button
                                onClick={() => setImportState(prev => ({ ...prev, step: ImportStep.PREVIEW }))}
                            >
                                التالي: معاينة البيانات
                            </Button>
                        </div>
                    </div>
                )}
            </CardContent>
        </Card>
    )
}

function DataPreviewStep({
    importState,
    setImportState
}: {
    importState: ImportState,
    setImportState: React.Dispatch<React.SetStateAction<ImportState>>
}) {
    const [showAllRows, setShowAllRows] = useState(false)

    // Convert data to spreadsheet format
    const spreadsheetData = importState.data.map(row =>
        row.map(cell => ({ value: cell || '' }))
    )

    // Limit preview to first 20 rows for performance
    const previewData = showAllRows ? spreadsheetData : spreadsheetData.slice(0, 20)
    const hasMoreRows = importState.data.length > 20

    // Get the actual data based on selected range
    const getSelectedData = () => {
        const { startRow, startCol, endRow, endCol } = importState.dataRange
        return importState.data
            .slice(startRow, endRow + 1)
            .map(row => row.slice(startCol, endCol + 1))
    }

    const getSelectedHeaders = () => {
        const { startRow, startCol, endCol } = importState.dataRange
        if (startRow >= importState.data.length) return []
        return importState.data[startRow]?.slice(startCol, endCol + 1) || []
    }

    const handleRangeChange = (field: keyof ImportState['dataRange'], value: number) => {
        setImportState(prev => ({
            ...prev,
            dataRange: {
                ...prev.dataRange,
                [field]: Math.max(0, value)
            }
        }))
    }

    const handleBack = () => {
        setImportState(prev => ({ ...prev, step: ImportStep.UPLOAD }))
    }

    const handleNext = () => {
        // Extract the selected data range
        const selectedData = getSelectedData()
        const selectedHeaders = getSelectedHeaders()

        if (selectedData.length === 0 || selectedHeaders.length === 0) {
            toast.error('يرجى تحديد نطاق بيانات صالح')
            return
        }

        // Update the state with the selected data
        setImportState(prev => ({
            ...prev,
            data: selectedData,
            headers: selectedHeaders.map(h => String(h || '')),
            step: ImportStep.MAPPING
        }))
    }

    return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center space-x-2 rtl:space-x-reverse">
                    <IconEye className="h-5 w-5" />
                    <span>معاينة البيانات</span>
                </CardTitle>
                <CardDescription>
                    تأكد من أن البيانات تم تحميلها بشكل صحيح قبل المتابعة
                </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
                {/* File Info */}
                <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg">
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                        <IconFile className="h-6 w-6 text-primary" />
                        <div>
                            <p className="font-medium">{importState.file?.name}</p>
                            <p className="text-sm text-muted-foreground">
                                {importState.data.length - 1} صف من البيانات، {importState.headers.length} عمود
                            </p>
                        </div>
                    </div>
                    <div className="text-sm text-muted-foreground">
                        {(importState.file?.size || 0 / 1024).toFixed(1)} كيلوبايت
                    </div>
                </div>

                {/* Data Range Selection */}
                <div className="space-y-4">
                    <h3 className="text-lg font-medium">تحديد نطاق البيانات</h3>
                    <div className="p-4 border rounded-lg bg-blue-50">
                        <p className="text-sm text-blue-800 mb-4">
                            حدد الصف والعمود الذي تبدأ منه البيانات الفعلية (عادة صف العناوين)
                        </p>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="startRow">صف البداية</Label>
                                <Input
                                    id="startRow"
                                    type="number"
                                    min="0"
                                    max={importState.data.length - 1}
                                    value={importState.dataRange.startRow}
                                    onChange={(e) => handleRangeChange('startRow', parseInt(e.target.value) || 0)}
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="startCol">عمود البداية</Label>
                                <Input
                                    id="startCol"
                                    type="number"
                                    min="0"
                                    max={importState.data[0]?.length - 1 || 0}
                                    value={importState.dataRange.startCol}
                                    onChange={(e) => handleRangeChange('startCol', parseInt(e.target.value) || 0)}
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="endRow">صف النهاية</Label>
                                <Input
                                    id="endRow"
                                    type="number"
                                    min={importState.dataRange.startRow}
                                    max={importState.data.length - 1}
                                    value={importState.dataRange.endRow}
                                    onChange={(e) => handleRangeChange('endRow', parseInt(e.target.value) || 0)}
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="endCol">عمود النهاية</Label>
                                <Input
                                    id="endCol"
                                    type="number"
                                    min={importState.dataRange.startCol}
                                    max={importState.data[0]?.length - 1 || 0}
                                    value={importState.dataRange.endCol}
                                    onChange={(e) => handleRangeChange('endCol', parseInt(e.target.value) || 0)}
                                />
                            </div>
                        </div>
                        <div className="mt-4 space-y-3">
                            <div className="text-sm text-blue-700">
                                <p>النطاق المحدد: الصفوف {importState.dataRange.startRow + 1} إلى {importState.dataRange.endRow + 1}، الأعمدة {importState.dataRange.startCol + 1} إلى {importState.dataRange.endCol + 1}</p>
                                <p>عدد صفوف البيانات: {Math.max(0, importState.dataRange.endRow - importState.dataRange.startRow)}</p>
                            </div>
                            <div className="flex gap-2 flex-wrap">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => setImportState(prev => ({
                                        ...prev,
                                        dataRange: {
                                            startRow: 0,
                                            startCol: 0,
                                            endRow: prev.data.length - 1,
                                            endCol: (prev.data[0]?.length || 1) - 1
                                        }
                                    }))}
                                >
                                    تحديد الكل
                                </Button>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => setImportState(prev => ({
                                        ...prev,
                                        dataRange: {
                                            startRow: 4,
                                            startCol: 0,
                                            endRow: prev.data.length - 1,
                                            endCol: (prev.data[0]?.length || 1) - 1
                                        }
                                    }))}
                                >
                                    بداية من الصف 5
                                </Button>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => setImportState(prev => ({
                                        ...prev,
                                        dataRange: {
                                            startRow: 1,
                                            startCol: 0,
                                            endRow: prev.data.length - 1,
                                            endCol: (prev.data[0]?.length || 1) - 1
                                        }
                                    }))}
                                >
                                    تجاهل الصف الأول
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Data Preview */}
                <div className="space-y-4">
                    <div className="flex items-center justify-between">
                        <h3 className="text-lg font-medium">معاينة البيانات المحددة</h3>
                        {hasMoreRows && (
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setShowAllRows(!showAllRows)}
                            >
                                {showAllRows ? 'إظهار أول 20 صف فقط' : `إظهار جميع الصفوف (${importState.data.length})`}
                            </Button>
                        )}
                    </div>

                    {/* Spreadsheet Preview */}
                    <div className="border rounded-lg overflow-hidden">
                        <div className="bg-muted p-2 border-b text-sm">
                            <p>معاينة النطاق المحدد - العناوين: {getSelectedHeaders().join(' | ')}</p>
                        </div>
                        <div className="max-h-96 overflow-auto">
                            <Spreadsheet
                                data={getSelectedData().slice(0, 10).map(row =>
                                    row.map(cell => ({ value: cell || '' }))
                                )}
                                columnLabels={getSelectedHeaders().map(h => String(h || ''))}
                                hideRowIndicators={false}
                                hideColumnIndicators={false}
                                className="w-full"
                            />
                        </div>
                        <div className="bg-muted p-2 border-t text-xs text-muted-foreground">
                            يتم عرض أول 10 صفوف من النطاق المحدد فقط
                        </div>
                    </div>

                    {!showAllRows && hasMoreRows && (
                        <p className="text-sm text-muted-foreground text-center">
                            يتم عرض أول 20 صف فقط. انقر "إظهار جميع الصفوف" لرؤية البيانات كاملة.
                        </p>
                    )}
                </div>

                {/* Data Summary */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="p-4 border rounded-lg text-center">
                        <div className="text-2xl font-bold text-primary">{Math.max(0, importState.dataRange.endRow - importState.dataRange.startRow)}</div>
                        <div className="text-sm text-muted-foreground">صف من البيانات (بدون العناوين)</div>
                    </div>
                    <div className="p-4 border rounded-lg text-center">
                        <div className="text-2xl font-bold text-primary">{getSelectedHeaders().length}</div>
                        <div className="text-sm text-muted-foreground">عمود محدد</div>
                    </div>
                    <div className="p-4 border rounded-lg text-center">
                        <div className="text-2xl font-bold text-primary">
                            {getSelectedData().slice(1).filter(row => row.some(cell => cell !== null && cell !== undefined && cell !== '')).length}
                        </div>
                        <div className="text-sm text-muted-foreground">صف يحتوي على بيانات</div>
                    </div>
                </div>

                {/* Navigation */}
                <div className="flex justify-between">
                    <Button variant="outline" onClick={handleBack}>
                        <IconArrowLeft className="h-4 w-4 ml-2 rtl:mr-2" />
                        السابق
                    </Button>
                    <Button onClick={handleNext}>
                        التالي: ربط الأعمدة
                        <IconArrowRight className="h-4 w-4 mr-2 rtl:ml-2" />
                    </Button>
                </div>
            </CardContent>
        </Card>
    )
}

function ColumnMappingStep({
    importState,
    setImportState
}: {
    importState: ImportState,
    setImportState: React.Dispatch<React.SetStateAction<ImportState>>
}) {
    // Define available order fields for mapping
    const orderFields = [
        { key: 'SKIP', label: 'لا تربط هذا العمود', required: false },
        { key: 'code', label: 'رمز الطلب', required: true },
        { key: 'customer_name', label: 'اسم العميل', required: true },
        { key: 'customer_phone', label: 'هاتف العميل', required: true },
        { key: 'customer_address', label: 'عنوان العميل', required: true },
        { key: 'total_price', label: 'السعر الإجمالي', required: false },
        { key: 'notes', label: 'ملاحظات', required: false },
        { key: 'commission_fixed_rate', label: 'معدل العمولة الثابت', required: false },
        { key: 'final_customer_payment', label: 'الدفع النهائي للعميل', required: false },
        { key: 'deadline_date', label: 'تاريخ الموعد النهائي', required: false },
        { key: 'breakable', label: 'قابل للكسر', required: false },
    ]

    const handleMappingChange = (header: string, fieldKey: string) => {
        setImportState(prev => ({
            ...prev,
            mapping: {
                ...prev.mapping,
                [header]: fieldKey
            }
        }))
    }

    const handleBack = () => {
        setImportState(prev => ({ ...prev, step: ImportStep.PREVIEW }))
    }

    const handleNext = () => {
        // Validate required fields are mapped
        const requiredFields = orderFields.filter(f => f.required).map(f => f.key)
        const mappedFields = Object.values(importState.mapping).filter(v => v !== '' && v !== 'SKIP')
        const missingRequired = requiredFields.filter(field => !mappedFields.includes(field))

        if (missingRequired.length > 0) {
            const missingLabels = missingRequired.map(key =>
                orderFields.find(f => f.key === key)?.label
            ).join('، ')
            toast.error(`يجب ربط الحقول المطلوبة: ${missingLabels}`)
            return
        }

        setImportState(prev => ({ ...prev, step: ImportStep.PROCESSING }))
    }

    const getPreviewData = () => {
        // Show first 3 rows of data for preview
        return importState.data.slice(1, 4).map(row =>
            importState.headers.map((header, index) => ({
                header,
                value: row[index] || '',
                mappedTo: importState.mapping[header] || 'SKIP'
            }))
        )
    }

    const requiredFields = orderFields.filter(f => f.required).map(f => f.key)
    const mappedFields = Object.values(importState.mapping).filter(v => v !== '' && v !== 'SKIP')
    const missingRequired = requiredFields.filter(field => !mappedFields.includes(field))

    return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center space-x-2 rtl:space-x-reverse">
                    <IconLink className="h-5 w-5" />
                    <span>ربط الأعمدة</span>
                </CardTitle>
                <CardDescription>
                    اربط أعمدة الملف بحقول الطلب المناسبة. الحقول المطلوبة مميزة بعلامة *
                </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
                {/* Mapping Interface */}
                <div className="space-y-4">
                    <h3 className="text-lg font-medium">ربط الأعمدة</h3>
                    <div className="grid gap-4">
                        {importState.headers.map((header, index) => (
                            <div key={index} className="flex items-center space-x-4 rtl:space-x-reverse p-4 border rounded-lg">
                                <div className="flex-1">
                                    <Label className="text-sm font-medium">
                                        عمود: {header}
                                    </Label>
                                    <p className="text-xs text-muted-foreground mt-1">
                                        مثال: {importState.data[1]?.[index] || 'لا توجد بيانات'}
                                    </p>
                                </div>
                                <div className="flex-1">
                                    <Select
                                        value={importState.mapping[header] || 'SKIP'}
                                        onValueChange={(value) => handleMappingChange(header, value)}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="اختر حقل الطلب" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {orderFields.map((field) => (
                                                <SelectItem key={field.key} value={field.key}>
                                                    {field.label} {field.required && '*'}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>

                {/* Validation Status */}
                <div className="space-y-4">
                    <h3 className="text-lg font-medium">حالة التحقق</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="p-4 border rounded-lg">
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium">الحقول المطلوبة</span>
                                <span className={`text-sm ${missingRequired.length === 0 ? 'text-green-600' : 'text-red-600'}`}>
                                    {requiredFields.length - missingRequired.length} / {requiredFields.length}
                                </span>
                            </div>
                            {missingRequired.length > 0 && (
                                <p className="text-xs text-red-600 mt-2">
                                    مطلوب: {missingRequired.map(key =>
                                        orderFields.find(f => f.key === key)?.label
                                    ).join('، ')}
                                </p>
                            )}
                        </div>
                        <div className="p-4 border rounded-lg">
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium">الأعمدة المربوطة</span>
                                <span className="text-sm text-blue-600">
                                    {mappedFields.length} / {importState.headers.length}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Preview */}
                <div className="space-y-4">
                    <h3 className="text-lg font-medium">معاينة الربط</h3>
                    <div className="border rounded-lg overflow-hidden">
                        <div className="bg-muted p-3 border-b">
                            <p className="text-sm font-medium">أول 3 صفوف من البيانات</p>
                        </div>
                        <div className="p-4 space-y-3">
                            {getPreviewData().map((row, rowIndex) => (
                                <div key={rowIndex} className="space-y-2">
                                    <p className="text-sm font-medium text-muted-foreground">
                                        الصف {rowIndex + 2}
                                    </p>
                                    <div className="grid gap-2">
                                        {row.map((cell, cellIndex) => (
                                            <div key={cellIndex} className="flex items-center space-x-2 rtl:space-x-reverse text-sm">
                                                <span className="w-24 text-muted-foreground truncate">
                                                    {cell.header}:
                                                </span>
                                                <span className="flex-1 font-medium">
                                                    {cell.value}
                                                </span>
                                                {cell.mappedTo && cell.mappedTo !== 'SKIP' && (
                                                    <span className="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded">
                                                        → {orderFields.find(f => f.key === cell.mappedTo)?.label}
                                                    </span>
                                                )}
                                                {cell.mappedTo === 'SKIP' && (
                                                    <span className="text-xs text-gray-500 bg-gray-50 px-2 py-1 rounded">
                                                        تجاهل
                                                    </span>
                                                )}
                                            </div>
                                        ))}
                                    </div>
                                    {rowIndex < getPreviewData().length - 1 && <Separator />}
                                </div>
                            ))}
                        </div>
                    </div>
                </div>

                {/* Navigation */}
                <div className="flex justify-between">
                    <Button variant="outline" onClick={handleBack}>
                        <IconArrowLeft className="h-4 w-4 ml-2 rtl:mr-2" />
                        السابق
                    </Button>
                    <Button
                        onClick={handleNext}
                        disabled={missingRequired.length > 0}
                    >
                        التالي: معالجة البيانات
                        <IconArrowRight className="h-4 w-4 mr-2 rtl:ml-2" />
                    </Button>
                </div>
            </CardContent>
        </Card>
    )
}

function ProcessingStep({
    importState,
    setImportState
}: {
    importState: ImportState,
    setImportState: React.Dispatch<React.SetStateAction<ImportState>>
}) {
    const { selectedOffice } = useAuth()
    const [processing, setProcessing] = useState(false)
    const [currentRow, setCurrentRow] = useState(0)
    const [processedOrders, setProcessedOrders] = useState<Array<{ success: boolean; data: any; error?: string }>>([])

    const processOrders = async () => {
        if (!selectedOffice) {
            toast.error('لم يتم اختيار مكتب. يرجى تسجيل الدخول مرة أخرى')
            return
        }

        setProcessing(true)
        const results: Array<{ success: boolean; data: any; error?: string }> = []

        // Skip header row
        const dataRows = importState.data.slice(1)

        for (let i = 0; i < dataRows.length; i++) {
            setCurrentRow(i + 1)
            const row = dataRows[i]

            try {
                // Map row data to order fields
                const orderData = mapRowToOrder(row, importState.headers, importState.mapping, selectedOffice.id)

                // Skip empty rows
                if (!orderData.customer_name || !orderData.code) {
                    results.push({
                        success: false,
                        data: row,
                        error: 'صف فارغ أو بيانات ناقصة (اسم العميل أو رمز الطلب مفقود)'
                    })
                    continue
                }

                // Create order via API
                const response = await ordersApisCreateOrder({
                    body: orderData
                })

                if (response.data) {
                    results.push({
                        success: true,
                        data: response.data
                    })
                } else {
                    results.push({
                        success: false,
                        data: row,
                        error: 'فشل في إنشاء الطلب - استجابة غير صالحة من الخادم'
                    })
                }

            } catch (error: any) {
                console.error('Error creating order:', error)
                let errorMessage = 'خطأ غير معروف'

                if (error.response?.data) {
                    // Handle validation errors from the API
                    if (typeof error.response.data === 'string') {
                        errorMessage = error.response.data
                    } else if (error.response.data.detail) {
                        errorMessage = error.response.data.detail
                    } else {
                        errorMessage = JSON.stringify(error.response.data)
                    }
                } else if (error.message) {
                    errorMessage = error.message
                }

                results.push({
                    success: false,
                    data: row,
                    error: errorMessage
                })
            }

            // Small delay to prevent overwhelming the server
            await new Promise(resolve => setTimeout(resolve, 100))
        }

        setProcessedOrders(results)

        // Update import state with results
        const successful = results.filter(r => r.success).length
        const failed = results.filter(r => !r.success).length
        const errors = results.filter(r => !r.success).map((r, index) => ({
            row: index + 2, // +2 because we skip header and arrays are 0-indexed
            data: r.data,
            error: r.error || 'خطأ غير معروف'
        }))

        setImportState(prev => ({
            ...prev,
            results: {
                total: results.length,
                successful,
                failed,
                errors
            },
            step: ImportStep.RESULTS
        }))

        setProcessing(false)

        if (successful > 0) {
            toast.success(`تم إنشاء ${successful} طلب بنجاح`)
        }
        if (failed > 0) {
            toast.error(`فشل في إنشاء ${failed} طلب`)
        }
    }

    const mapRowToOrder = (
        row: any[],
        headers: string[],
        mapping: Record<string, string>,
        officeId: string
    ): CreateOrderSchema => {
        const orderData: Partial<CreateOrderSchema> = {
            office_id: officeId,
            handling_status: 'PENDING' as OrderHandlingStatusSchema,
            notes: '',
            breakable: false,
            total_price: null,
            customer_company_id: null,
            deadline_date: null,
            commission_fixed_rate: null,
            assigned_to_id: null,
            final_customer_payment: null,
            cancellation_reason_template_id: null,
            cancellation_reason: null
        }

        // Map each column to its corresponding order field
        headers.forEach((header, index) => {
            const fieldKey = mapping[header]
            if (!fieldKey || fieldKey === '' || fieldKey === 'SKIP') return

            const cellValue = row[index]
            if (cellValue === null || cellValue === undefined || cellValue === '') return

            switch (fieldKey) {
                case 'code':
                case 'customer_name':
                case 'customer_phone':
                case 'customer_address':
                case 'notes':
                    orderData[fieldKey] = String(cellValue).trim()
                    break
                case 'total_price':
                case 'commission_fixed_rate':
                case 'final_customer_payment':
                    const numValue = parseFloat(String(cellValue))
                    orderData[fieldKey] = isNaN(numValue) ? null : numValue
                    break
                case 'breakable':
                    const boolValue = String(cellValue).toLowerCase()
                    orderData[fieldKey] = boolValue === 'true' || boolValue === '1' || boolValue === 'نعم'
                    break
                case 'deadline_date':
                    // Try to parse date
                    try {
                        const dateValue = new Date(cellValue)
                        if (!isNaN(dateValue.getTime())) {
                            orderData[fieldKey] = dateValue.toISOString()
                        }
                    } catch {
                        // Invalid date, leave as null
                    }
                    break
            }
        })

        return orderData as CreateOrderSchema
    }

    const progress = processing ? (currentRow / (importState.data.length - 1)) * 100 : 0

    return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center space-x-2 rtl:space-x-reverse">
                    <IconLoader2 className={`h-5 w-5 ${processing ? 'animate-spin' : ''}`} />
                    <span>معالجة البيانات</span>
                </CardTitle>
                <CardDescription>
                    {processing ? 'جاري إنشاء الطلبات...' : 'جاهز لبدء معالجة البيانات'}
                </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
                {!processing && processedOrders.length === 0 && (
                    <div className="text-center py-8">
                        <div className="space-y-4">
                            <div className="p-4 bg-blue-50 rounded-lg">
                                <h3 className="font-medium text-blue-900 mb-2">جاهز للمعالجة</h3>
                                <p className="text-sm text-blue-700">
                                    سيتم إنشاء {importState.data.length - 1} طلب جديد في المكتب: {selectedOffice?.name}
                                </p>
                            </div>
                            <Button onClick={processOrders} size="lg">
                                بدء معالجة الطلبات
                            </Button>
                        </div>
                    </div>
                )}

                {processing && (
                    <div className="space-y-4">
                        <div className="text-center">
                            <p className="text-lg font-medium mb-2">
                                معالجة الصف {currentRow} من {importState.data.length - 1}
                            </p>
                            <Progress value={progress} className="w-full" />
                            <p className="text-sm text-muted-foreground mt-2">
                                {Math.round(progress)}% مكتمل
                            </p>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="p-4 border rounded-lg text-center">
                                <div className="text-2xl font-bold text-green-600">
                                    {processedOrders.filter(r => r.success).length}
                                </div>
                                <div className="text-sm text-muted-foreground">نجح</div>
                            </div>
                            <div className="p-4 border rounded-lg text-center">
                                <div className="text-2xl font-bold text-red-600">
                                    {processedOrders.filter(r => !r.success).length}
                                </div>
                                <div className="text-sm text-muted-foreground">فشل</div>
                            </div>
                        </div>
                    </div>
                )}
            </CardContent>
        </Card>
    )
}

function ResultsStep({
    importState,
    setImportState
}: {
    importState: ImportState,
    setImportState: React.Dispatch<React.SetStateAction<ImportState>>
}) {
    const navigate = useNavigate()

    const handleStartNew = () => {
        setImportState({
            step: ImportStep.UPLOAD,
            file: null,
            data: [],
            headers: [],
            mapping: {},
            dataRange: {
                startRow: 0,
                startCol: 0,
                endRow: 0,
                endCol: 0
            },
            results: {
                total: 0,
                successful: 0,
                failed: 0,
                errors: []
            }
        })
    }

    const handleGoToOrders = () => {
        navigate({ to: '/orders' })
    }

    const exportErrors = () => {
        if (importState.results.errors.length === 0) return

        const errorData = [
            ['رقم الصف', 'البيانات', 'سبب الخطأ'],
            ...importState.results.errors.map(error => [
                error.row.toString(),
                Array.isArray(error.data) ? error.data.join(' | ') : JSON.stringify(error.data),
                error.error
            ])
        ]

        const ws = XLSX.utils.aoa_to_sheet(errorData)
        const wb = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(wb, ws, 'أخطاء الاستيراد')
        XLSX.writeFile(wb, `أخطاء_استيراد_الطلبات_${new Date().toISOString().split('T')[0]}.xlsx`)
    }

    return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center space-x-2 rtl:space-x-reverse">
                    <IconCheck className="h-5 w-5 text-green-600" />
                    <span>نتائج الاستيراد</span>
                </CardTitle>
                <CardDescription>
                    ملخص عملية استيراد الطلبات
                </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
                {/* Summary Cards */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="p-6 border rounded-lg text-center bg-blue-50">
                        <div className="text-3xl font-bold text-blue-600 mb-2">
                            {importState.results.total}
                        </div>
                        <div className="text-sm text-blue-700 font-medium">إجمالي الصفوف</div>
                    </div>
                    <div className="p-6 border rounded-lg text-center bg-green-50">
                        <div className="text-3xl font-bold text-green-600 mb-2">
                            {importState.results.successful}
                        </div>
                        <div className="text-sm text-green-700 font-medium">تم إنشاؤها بنجاح</div>
                    </div>
                    <div className="p-6 border rounded-lg text-center bg-red-50">
                        <div className="text-3xl font-bold text-red-600 mb-2">
                            {importState.results.failed}
                        </div>
                        <div className="text-sm text-red-700 font-medium">فشل في الإنشاء</div>
                    </div>
                </div>

                {/* Success Message */}
                {importState.results.successful > 0 && (
                    <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                        <div className="flex items-center space-x-2 rtl:space-x-reverse">
                            <IconCheck className="h-5 w-5 text-green-600" />
                            <h3 className="font-medium text-green-800">
                                تم إنشاء {importState.results.successful} طلب بنجاح!
                            </h3>
                        </div>
                        <p className="text-sm text-green-700 mt-2">
                            يمكنك الآن العثور على الطلبات الجديدة في قائمة الطلبات.
                        </p>
                    </div>
                )}

                {/* Error Details */}
                {importState.results.failed > 0 && (
                    <div className="space-y-4">
                        <div className="flex items-center justify-between">
                            <h3 className="text-lg font-medium text-red-800">
                                الطلبات التي فشل إنشاؤها ({importState.results.failed})
                            </h3>
                            <Button variant="outline" size="sm" onClick={exportErrors}>
                                تصدير الأخطاء إلى Excel
                            </Button>
                        </div>

                        <div className="border rounded-lg overflow-hidden">
                            <div className="bg-red-50 p-3 border-b">
                                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                                    <IconAlertTriangle className="h-5 w-5 text-red-600" />
                                    <span className="font-medium text-red-800">تفاصيل الأخطاء</span>
                                </div>
                            </div>
                            <div className="max-h-96 overflow-auto">
                                {importState.results.errors.map((error, index) => (
                                    <div key={index} className="p-4 border-b last:border-b-0">
                                        <div className="flex items-start space-x-3 rtl:space-x-reverse">
                                            <div className="flex-shrink-0 w-12 h-8 bg-red-100 rounded flex items-center justify-center">
                                                <span className="text-sm font-medium text-red-600">
                                                    {error.row}
                                                </span>
                                            </div>
                                            <div className="flex-1 space-y-2">
                                                <div className="text-sm">
                                                    <span className="font-medium text-red-800">خطأ: </span>
                                                    <span className="text-red-700">{error.error}</span>
                                                </div>
                                                <div className="text-xs text-muted-foreground">
                                                    <span className="font-medium">البيانات: </span>
                                                    {Array.isArray(error.data)
                                                        ? error.data.slice(0, 3).join(' | ') + (error.data.length > 3 ? '...' : '')
                                                        : JSON.stringify(error.data).substring(0, 100) + '...'
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                )}

                {/* File Info */}
                <div className="p-4 bg-muted/50 rounded-lg">
                    <h3 className="font-medium mb-2">معلومات الملف المعالج</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                            <span className="text-muted-foreground">اسم الملف: </span>
                            <span className="font-medium">{importState.file?.name}</span>
                        </div>
                        <div>
                            <span className="text-muted-foreground">حجم الملف: </span>
                            <span className="font-medium">
                                {((importState.file?.size || 0) / 1024).toFixed(1)} كيلوبايت
                            </span>
                        </div>
                        <div>
                            <span className="text-muted-foreground">عدد الأعمدة: </span>
                            <span className="font-medium">{importState.headers.length}</span>
                        </div>
                        <div>
                            <span className="text-muted-foreground">وقت المعالجة: </span>
                            <span className="font-medium">{new Date().toLocaleString('ar-SA')}</span>
                        </div>
                    </div>
                </div>

                {/* Actions */}
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <Button onClick={handleGoToOrders} size="lg">
                        عرض جميع الطلبات
                    </Button>
                    <Button variant="outline" onClick={handleStartNew} size="lg">
                        استيراد ملف جديد
                    </Button>
                </div>
            </CardContent>
        </Card>
    )
}
