import { SiteHeader } from '@/components/site-header'
import { createFileRoute, useNavigate } from '@tanstack/react-router'
import { DataTableComponent } from '@/components/DataTableComponent'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useEffect, useState } from 'react'
import { ordersApisListOrders, ordersApisDeleteOrder, ordersApisListCompanies, accountsApisListUsers } from '@/client'
import { type OrderSchema, type CompanySchema, type UserSchema } from '@/client/types.gen'
import { useAuth } from '@/contexts/AuthContext'
import { toast } from 'sonner'
import { Separator } from '@/components/ui/separator'
import { IconFilter, IconSearch, IconX, IconLoader2, Icon<PERSON>ser, IconFileImport } from '@tabler/icons-react'

export const Route = createFileRoute('/orders/')({
    component: RouteComponent,
})

// Filter interface
interface OrderFilters {
    status: string
    search: string
    created_at_from: string
    created_at_to: string
    customer_company_id: string
    assigned_to_id: string
}

function RouteComponent() {
    const { selectedOffice } = useAuth()
    const [data, setData] = useState<OrderSchema[]>([])
    const [loading, setLoading] = useState(false)
    const [showFilters, setShowFilters] = useState(true)
    const [companies, setCompanies] = useState<CompanySchema[]>([])
    const [users, setUsers] = useState<UserSchema[]>([])
    const navigate = useNavigate()

    // Filter state
    const [filters, setFilters] = useState<OrderFilters>({
        status: '',
        search: '',
        created_at_from: '',
        created_at_to: '',
        customer_company_id: '',
        assigned_to_id: '',
    })

    const fetchData = async (filterParams?: Partial<OrderFilters>) => {
        if (!selectedOffice) return

        setLoading(true)
        try {
            const queryParams: any = {
                office_id: selectedOffice.id,
                page_size: 100,
            }

            // Add filter parameters
            const activeFilters = filterParams || filters
            if (activeFilters.status) queryParams.status = activeFilters.status
            if (activeFilters.search) queryParams.search = activeFilters.search
            if (activeFilters.created_at_from) queryParams.created_at_from = activeFilters.created_at_from
            if (activeFilters.created_at_to) queryParams.created_at_to = activeFilters.created_at_to
            if (activeFilters.customer_company_id) queryParams.customer_company_id = activeFilters.customer_company_id
            if (activeFilters.assigned_to_id) queryParams.assigned_to_id = activeFilters.assigned_to_id

            const response = await ordersApisListOrders({
                query: queryParams,
            })
            if (response.data) {
                setData(response.data.orders)
            }
        } catch (error) {
            console.error('Error fetching orders:', error)
            toast.error('حدث خطأ أثناء تحميل بيانات الطلبات')
        } finally {
            setLoading(false)
        }
    }

    const fetchCompanies = async () => {
        if (!selectedOffice) return

        try {
            const response = await ordersApisListCompanies({
                query: {
                    office_id: selectedOffice.id,
                    page_size: 100, // Get all companies
                },
            })
            if (response.data) {
                setCompanies(response.data.companies)
            }
        } catch (error) {
            console.error('Error fetching companies:', error)
            // Don't show error toast for companies as it's not critical
        }
    }

    const fetchUsers = async () => {
        if (!selectedOffice) return

        try {
            const response = await accountsApisListUsers({
                query: {
                    office_id: selectedOffice.id,
                    page_size: 100, // Get all users
                },
            })
            if (response.data) {
                setUsers(response.data.users)
            }
        } catch (error) {
            console.error('Error fetching users:', error)
            // Don't show error toast for users as it's not critical
        }
    }

    useEffect(() => {
        fetchData()
        fetchCompanies()
        fetchUsers()
    }, [selectedOffice])

    const handleFilterChange = (key: keyof OrderFilters, value: string) => {
        setFilters(prev => ({ ...prev, [key]: value }))
    }

    const handleApplyFilters = () => {
        fetchData(filters)
    }

    const handleClearFilters = () => {
        const clearedFilters = {
            status: '',
            search: '',
            created_at_from: '',
            created_at_to: '',
            customer_company_id: '',
            assigned_to_id: '',
        }
        setFilters(clearedFilters)
        fetchData(clearedFilters)
    }

    const handleEditOrder = (order: OrderSchema) => {
        navigate({
            to: "/orders/form",
            search: { id: order.id }
        })
    }

    const handleViewOrder = (order: OrderSchema) => {
        // For now, we'll just show a toast with order details
        // In the future, this could navigate to a detailed view page
        toast.info(`عرض تفاصيل الطلب: ${order.code} - ${order.customer_name}`)
    }

    const handleDeleteOrder = async (order: OrderSchema) => {
        if (confirm('هل أنت متأكد من حذف هذا الطلب؟')) {
            try {
                await ordersApisDeleteOrder({
                    path: {
                        order_id: order.id,
                    },
                })

                toast.success('تم حذف الطلب بنجاح')
                fetchData() // Refresh the data
            } catch (error) {
                console.error('Error deleting order:', error)
                toast.error('حدث خطأ أثناء حذف الطلب')
            }
        }
    }

    const getStatusLabel = (status: string) => {
        const statusLabels = {
            PENDING: "في الانتظار",
            ASSIGNED: "تم التعيين",
            PROCESSING: "قيد المعالجة",
            CANCELLED: "ملغي",
            DELIVERED: "تم التسليم"
        }
        return statusLabels[status as keyof typeof statusLabels] || status
    }

    const getStatusColor = (status: string) => {
        const statusColors = {
            PENDING: "bg-yellow-100 text-yellow-800",
            ASSIGNED: "bg-blue-100 text-blue-800",
            PROCESSING: "bg-orange-100 text-orange-800",
            CANCELLED: "bg-red-100 text-red-800",
            DELIVERED: "bg-green-100 text-green-800"
        }
        return statusColors[status as keyof typeof statusColors] || "bg-gray-100 text-gray-800"
    }

    const formatDate = (dateString: string | null) => {
        if (!dateString) return '-'
        return new Date(dateString).toLocaleDateString('ar-EG', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        })
    }

    const formatPrice = (price: number | null) => {
        if (price === null) return '-'
        return `${price.toFixed(2)} ج.م`
    }

    const hasActiveFilters = Object.values(filters).some(value => value !== '')

    return <>
        <SiteHeader title="الطلبات" />
        <div className="flex flex-col m-4">
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-2xl font-bold">الطلبات</h1>
                    <p className="text-sm text-muted-foreground">هنا يمكنك إدارة الطلبات</p>
                </div>
                <div className="flex items-center gap-2">
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => navigate({ to: '/orders/bulk-import' })}
                        className="flex items-center gap-2"
                    >
                        <IconFileImport size={16} />
                        استيراد بالجملة
                    </Button>
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => navigate({ to: '/orders/assign' })}
                        className="flex items-center gap-2"
                    >
                        <IconUser size={16} />
                        تخصيص سريع
                    </Button>
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setShowFilters(!showFilters)}
                        className="flex items-center gap-2"
                    >
                        <IconFilter size={16} />
                        فلاتر
                        {hasActiveFilters && (
                            <Badge variant="secondary" className="ml-1">
                                {Object.values(filters).filter(v => v !== '').length}
                            </Badge>
                        )}
                    </Button>
                </div>
            </div>
        </div>
        <Separator className="mb-4" />

        {/* Filters Section */}
        {showFilters && (
            <Card className="m-4 mb-6">
                <CardHeader>
                    <CardTitle className="text-lg">فلاتر البحث</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {/* Search */}
                        <div className="space-y-2">
                            <label className="text-sm font-medium">البحث</label>
                            <div className="relative">
                                <IconSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                                <Input
                                    placeholder="البحث في رمز الطلب، اسم العميل، الهاتف، العنوان..."
                                    value={filters.search}
                                    onChange={(e) => handleFilterChange('search', e.target.value)}
                                    className="pl-10"
                                    onKeyDown={(e) => {
                                        if (e.key === 'Enter') {
                                            handleApplyFilters()
                                        }
                                    }}
                                />
                            </div>
                        </div>

                        {/* Status Filter */}
                        <div className="space-y-2">
                            <label className="text-sm font-medium">حالة الطلب</label>
                            <Select value={filters.status || "all"} onValueChange={(value) => handleFilterChange('status', value === "all" ? "" : value)}>
                                <SelectTrigger>
                                    <SelectValue placeholder="جميع الحالات" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">جميع الحالات</SelectItem>
                                    <SelectItem value="PENDING">في الانتظار</SelectItem>
                                    <SelectItem value="ASSIGNED">تم التعيين</SelectItem>
                                    <SelectItem value="PROCESSING">قيد المعالجة</SelectItem>
                                    <SelectItem value="CANCELLED">ملغي</SelectItem>
                                    <SelectItem value="DELIVERED">تم التسليم</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        {/* Date From */}
                        <div className="space-y-2">
                            <label className="text-sm font-medium">من تاريخ</label>
                            <Input
                                type="date"
                                value={filters.created_at_from}
                                onChange={(e) => handleFilterChange('created_at_from', e.target.value)}
                            />
                        </div>

                        {/* Date To */}
                        <div className="space-y-2">
                            <label className="text-sm font-medium">إلى تاريخ</label>
                            <Input
                                type="date"
                                value={filters.created_at_to}
                                onChange={(e) => handleFilterChange('created_at_to', e.target.value)}
                            />
                        </div>

                        {/* Company Filter */}
                        <div className="space-y-2">
                            <label className="text-sm font-medium">الشركة</label>
                            <Select value={filters.customer_company_id || "all"} onValueChange={(value) => handleFilterChange('customer_company_id', value === "all" ? "" : value)}>
                                <SelectTrigger>
                                    <SelectValue placeholder="جميع الشركات" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">جميع الشركات</SelectItem>
                                    {companies.map((company) => (
                                        <SelectItem key={company.id} value={company.id}>
                                            <div className="flex items-center gap-2">
                                                <div
                                                    className="w-3 h-3 rounded-full"
                                                    style={{ backgroundColor: company.color_code || '#ccc' }}
                                                />
                                                {company.name}
                                            </div>
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>

                        {/* Assigned To Filter */}
                        <div className="space-y-2">
                            <label className="text-sm font-medium">المسؤول</label>
                            <Select value={filters.assigned_to_id || "all"} onValueChange={(value) => handleFilterChange('assigned_to_id', value === "all" ? "" : value)}>
                                <SelectTrigger>
                                    <SelectValue placeholder="جميع المسؤولين" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">جميع المسؤولين</SelectItem>
                                    {users.length > 0 ? (
                                        users.map((user) => (
                                            <SelectItem key={user.id} value={user.id}>
                                                {user.first_name} {user.last_name}
                                            </SelectItem>
                                        ))
                                    ) : (
                                        <SelectItem value="no-data" disabled>
                                            لا توجد بيانات متاحة
                                        </SelectItem>
                                    )}
                                </SelectContent>
                            </Select>
                        </div>
                    </div>

                    {/* Filter Actions */}
                    <div className="flex items-center justify-end gap-2 mt-6">
                        <Button
                            variant="outline"
                            onClick={handleClearFilters}
                            className="flex items-center gap-2"
                        >
                            <IconX size={16} />
                            مسح الفلاتر
                        </Button>
                        <Button
                            onClick={handleApplyFilters}
                            disabled={loading}
                            className="flex items-center gap-2"
                        >
                            {loading && <IconLoader2 className="animate-spin" size={16} />}
                            تطبيق الفلاتر
                        </Button>
                    </div>
                </CardContent>
            </Card>
        )}

        <div className="flex flex-col m-4">
            <DataTableComponent
                idField='id'
                pageSize={10}
                enablePagination={true}
                showAddButton={true}
                addButtonText="إضافة طلب"
                onAddClick={() => {
                    navigate({
                        to: "/orders/form",
                        search: { id: undefined }
                    })
                }}
                data={data}
                columns={
                    [
                        {
                            header: "رمز الطلب",
                            accessorKey: "code",
                            cell: ({ row }) => (
                                <div
                                    className="flex flex-col cursor-pointer hover:bg-gray-50 p-2 rounded transition"
                                    onClick={() => {
                                        navigate({
                                            to: "/orders/form",
                                            search: { id: row.original.id }
                                        })
                                    }}
                                    title="تعديل الطلب"
                                >
                                    <span className="font-medium">
                                        {row.original.code}
                                    </span>
                                    <span className="text-sm text-muted-foreground">
                                        {formatDate(row.original.created_at)}
                                    </span>
                                </div>
                            ),
                        },
                        {
                            header: "العميل",
                            accessorKey: "customer_name",
                            cell: ({ row }) => (
                                <div className="flex flex-col">
                                    <span className="font-medium">
                                        {row.original.customer_name}
                                    </span>
                                    <span className="text-sm text-muted-foreground">
                                        {row.original.customer_phone}
                                    </span>
                                </div>
                            ),
                        },
                        {
                            header: "العنوان",
                            accessorKey: "customer_address",
                            cell: ({ row }) => (
                                <span className="text-sm max-w-xs truncate">
                                    {row.original.customer_address || '-'}
                                </span>
                            ),
                        },
                        {
                            header: "السعر الإجمالي",
                            accessorKey: "total_price",
                            cell: ({ row }) => (
                                <span className="font-medium">
                                    {formatPrice(row.original.total_price)}
                                </span>
                            ),
                        },
                        {
                            header: "الدفع النهائي",
                            accessorKey: "final_customer_payment",
                            cell: ({ row }) => (
                                <span className="font-medium">
                                    {formatPrice(row.original.final_customer_payment)}
                                </span>
                            ),
                        },
                        {
                            header: "الحالة",
                            accessorKey: "handling_status",
                            cell: ({ row }) => (
                                <Badge className={getStatusColor(row.original.handling_status)}>
                                    {getStatusLabel(row.original.handling_status)}
                                </Badge>
                            ),
                        },
                        {
                            header: "تاريخ الاستحقاق",
                            accessorKey: "deadline_date",
                            cell: ({ row }) => (
                                <span className="text-sm">
                                    {formatDate(row.original.deadline_date)}
                                </span>
                            ),
                        },
                    ]
                }
                actionsColumn={{
                    enableEdit: false, // We'll use custom actions instead
                    enableDelete: false, // We'll use custom actions instead
                    customActions: [
                        {
                            label: "عرض",
                            onClick: handleViewOrder,
                        },
                        {
                            label: "تعديل",
                            onClick: handleEditOrder,
                        },
                        {
                            label: "حذف",
                            onClick: handleDeleteOrder,
                            variant: "destructive",
                        },
                    ],
                }}
            />
        </div>
    </>
}
