import { Outlet, createRootRoute, useLocation } from '@tanstack/react-router'
import { TanStackRouterDevtools } from '@tanstack/react-router-devtools'
import { useAuth } from '../contexts/AuthContext'
import { SidebarInset, SidebarProvider } from '@/components/ui/sidebar'
import { AppSidebar } from '@/components/app-sidebar'

function RootComponent() {
  const location = useLocation()
  const { isAuthenticated } = useAuth()

  // Don't use layout for login page
  const isLoginPage = location.pathname === '/login'

  if (isLoginPage || !isAuthenticated) {
    return (
      <>
        <Outlet />
        <TanStackRouterDevtools />
      </>
    )
  }

  return (
    <>
      <SidebarProvider>
        <AppSidebar variant="inset" />
        <SidebarInset>
          <Outlet />
          <TanStackRouterDevtools />
        </SidebarInset>
      </SidebarProvider>
    </>
  )
}

export const Route = createRootRoute({
  component: RootComponent,
})
