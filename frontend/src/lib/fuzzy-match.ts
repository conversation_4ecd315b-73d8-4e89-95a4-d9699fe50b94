/**
 * Fuzzy matching utility for grouping similar addresses
 */

export interface FuzzyMatchOptions {
    threshold?: number; // Similarity threshold (0-1), default 0.8
    ignoreCase?: boolean; // Whether to ignore case, default true
    normalizeWhitespace?: boolean; // Whether to normalize whitespace, default true
}

export interface AddressGroup {
    representativeAddress: string;
    orders: string[]; // Array of order IDs
    similarity: number;
}

/**
 * Calculate Levenshtein distance between two strings
 */
function levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

    for (let i = 0; i <= str1.length; i++) {
        matrix[0][i] = i;
    }

    for (let j = 0; j <= str2.length; j++) {
        matrix[j][0] = j;
    }

    for (let j = 1; j <= str2.length; j++) {
        for (let i = 1; i <= str1.length; i++) {
            const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
            matrix[j][i] = Math.min(
                matrix[j][i - 1] + 1, // deletion
                matrix[j - 1][i] + 1, // insertion
                matrix[j - 1][i - 1] + indicator // substitution
            );
        }
    }

    return matrix[str2.length][str1.length];
}

/**
 * Calculate similarity between two strings (0-1)
 */
function calculateSimilarity(str1: string, str2: string): number {
    const maxLength = Math.max(str1.length, str2.length);
    if (maxLength === 0) return 1;

    const distance = levenshteinDistance(str1, str2);
    return 1 - (distance / maxLength);
}

/**
 * Normalize address for better matching
 */
function normalizeAddress(address: string, options: FuzzyMatchOptions = {}): string {
    let normalized = address;

    if (options.ignoreCase !== false) {
        normalized = normalized.toLowerCase();
    }

    if (options.normalizeWhitespace !== false) {
        // Remove extra whitespace and normalize
        normalized = normalized.replace(/\s+/g, ' ').trim();
    }

    // Remove common punctuation that doesn't affect address matching
    normalized = normalized.replace(/[.,;:!?]/g, '');

    return normalized;
}

/**
 * Group addresses using fuzzy matching
 */
export function groupAddresses(
    addresses: Array<{ id: string; address: string }>,
    options: FuzzyMatchOptions = {}
): AddressGroup[] {
    const {
        threshold = 0.8,
        ignoreCase = true,
        normalizeWhitespace = true
    } = options;

    const groups: AddressGroup[] = [];
    const processed = new Set<string>();

    for (const item of addresses) {
        if (processed.has(item.id)) continue;

        const normalizedAddress = normalizeAddress(item.address, { ignoreCase, normalizeWhitespace });
        const group: AddressGroup = {
            representativeAddress: item.address,
            orders: [item.id],
            similarity: 1
        };

        processed.add(item.id);

        // Find similar addresses
        for (const otherItem of addresses) {
            if (processed.has(otherItem.id)) continue;

            const otherNormalized = normalizeAddress(otherItem.address, { ignoreCase, normalizeWhitespace });
            const similarity = calculateSimilarity(normalizedAddress, otherNormalized);

            if (similarity >= threshold) {
                group.orders.push(otherItem.id);
                processed.add(otherItem.id);

                // Update representative address to the longer/more detailed one
                if (otherItem.address.length > group.representativeAddress.length) {
                    group.representativeAddress = otherItem.address;
                }
            }
        }

        groups.push(group);
    }

    return groups;
}

/**
 * Find the best matching address group for a given address
 */
export function findBestMatch(
    address: string,
    groups: AddressGroup[],
    options: FuzzyMatchOptions = {}
): { group: AddressGroup; similarity: number } | null {
    const {
        threshold = 0.8,
        ignoreCase = true,
        normalizeWhitespace = true
    } = options;

    const normalizedAddress = normalizeAddress(address, { ignoreCase, normalizeWhitespace });
    let bestMatch: { group: AddressGroup; similarity: number } | null = null;
    let bestSimilarity = 0;

    for (const group of groups) {
        const normalizedGroupAddress = normalizeAddress(group.representativeAddress, { ignoreCase, normalizeWhitespace });
        const similarity = calculateSimilarity(normalizedAddress, normalizedGroupAddress);

        if (similarity >= threshold && similarity > bestSimilarity) {
            bestSimilarity = similarity;
            bestMatch = { group, similarity };
        }
    }

    return bestMatch;
} 